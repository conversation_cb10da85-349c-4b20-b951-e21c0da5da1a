#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义HTC和Beta预测脚本
允许用户指定xyz坐标文件和工况参数进行预测
"""

import os
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from tqdm import tqdm
import argparse
import json

from htc_beta_model import HTCBetaNet
from prepare_htc_beta_dataset import load_stats

class CustomHTCBetaPredictor:
    """自定义HTC和Beta预测器"""
    
    def __init__(self, model_path):
        """
        初始化预测器
        Args:
            model_path: 模型检查点路径
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 加载模型检查点
        print(f"正在加载模型: {model_path}")
        checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
        
        # 获取模型配置和统计数据
        self.config = checkpoint['config']
        self.stats = checkpoint['stats']
        
        # 创建模型
        self.model = HTCBetaNet(
            xyz_dim=3,
            condition_dim=5,
            embed_dim=self.config['embed_dim'],
            num_geo_layers=self.config['num_geo_layers'],
            num_heads=self.config['num_heads'],
            hidden_dim=self.config['hidden_dim'],
            dropout=self.config['dropout']
        ).to(self.device)
        
        # 加载模型权重
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        print("模型加载完成！")
        self._print_stats()
    
    def _print_stats(self):
        """打印统计信息"""
        print("\n=== 模型信息 ===")
        print("模型已加载，支持任意参数范围预测")
        print("支持的输入:")
        print("  - XYZ坐标: 任意翼型表面坐标")
        print("  - 速度: 任意值 (m/s)")
        print("  - 温度: 任意值 (°C)")
        print("  - LWC: 任意值")
        print("  - MVD: 任意值 (μm)")
        print("  - 时间: 0-9 (对应1-10分钟)")
        print("=" * 40)
    
    def _normalize_data(self, data, data_type):
        """归一化数据"""
        if data_type in ['xyz', 'conditions']:
            min_val = self.stats[data_type]['min']
            max_val = self.stats[data_type]['max']
            return (data - min_val) / (max_val - min_val + 1e-8)
        else:  # htc, beta
            min_val = self.stats[data_type]['min']
            max_val = self.stats[data_type]['max']
            return (data - min_val) / (max_val - min_val + 1e-8)
    
    def _denormalize_predictions(self, htc_norm, beta_norm):
        """反归一化预测结果"""
        htc_min, htc_max = self.stats['htc']['min'], self.stats['htc']['max']
        beta_min, beta_max = self.stats['beta']['min'], self.stats['beta']['max']
        
        htc = htc_norm * (htc_max - htc_min) + htc_min
        beta = beta_norm * (beta_max - beta_min) + beta_min
        
        return htc, beta
    
    def load_xyz_coordinates(self, xyz_file_path):
        """
        加载xyz坐标文件
        Args:
            xyz_file_path: xyz坐标文件路径 (txt格式，每行为 x y z)
        Returns:
            numpy array: shape (N, 3) 的坐标数组
        """
        print(f"正在加载坐标文件: {xyz_file_path}")
        
        try:
            # 尝试不同的加载方式
            if xyz_file_path.endswith('.txt'):
                # 纯文本文件，可能有header
                try:
                    coords = np.loadtxt(xyz_file_path)
                except ValueError:
                    # 可能有header，跳过第一行
                    coords = np.loadtxt(xyz_file_path, skiprows=1)
            elif xyz_file_path.endswith('.dat'):
                # dat文件，可能包含更多列
                data = np.loadtxt(xyz_file_path)
                if data.ndim == 2 and data.shape[1] >= 3:
                    coords = data[:, :3]  # 只取前3列作为xyz
                else:
                    coords = data
            else:
                # 默认按文本文件处理
                try:
                    coords = np.loadtxt(xyz_file_path)
                except ValueError:
                    # 可能有header，跳过第一行
                    coords = np.loadtxt(xyz_file_path, skiprows=1)
            
            # 确保是2D数组
            if coords.ndim == 1:
                coords = coords.reshape(1, -1)
            
            # 检查维度
            if coords.shape[1] < 3:
                raise ValueError(f"坐标文件应至少包含3列(x,y,z)，但只有{coords.shape[1]}列")
            
            # 只取前3列
            coords = coords[:, :3]
            
            print(f"成功加载 {len(coords)} 个坐标点")
            print(f"坐标范围: X[{coords[:, 0].min():.6f}, {coords[:, 0].max():.6f}], "
                  f"Y[{coords[:, 1].min():.6f}, {coords[:, 1].max():.6f}], "
                  f"Z[{coords[:, 2].min():.6f}, {coords[:, 2].max():.6f}]")
            
            return coords
            
        except Exception as e:
            print(f"加载坐标文件失败: {e}")
            print("请确保文件格式正确，每行包含x y z坐标，用空格或制表符分隔")
            raise
    
    def predict_points(self, xyz_coords, velocity, temperature, lwc, mvd, time_step):
        """
        预测指定坐标点的HTC和Beta值
        Args:
            xyz_coords: numpy array, shape (N, 3) - xyz坐标
            velocity: float - 速度 (m/s)
            temperature: float - 温度 (°C)
            lwc: float - 液态水含量
            mvd: float - 平均体积直径 (μm)
            time_step: float - 时间步 (0-9对应1-10分钟)
        Returns:
            htc_pred: numpy array - HTC预测值
            beta_pred: numpy array - Beta预测值
        """
        print(f"\n正在预测 {len(xyz_coords)} 个点...")
        print(f"工况参数: 速度={velocity} m/s, 温度={temperature}°C, LWC={lwc}, MVD={mvd}μm, 时间={time_step+1}分钟")
        
        # 准备条件向量
        conditions = np.array([velocity, temperature, lwc, mvd, time_step])
        conditions = np.tile(conditions, (len(xyz_coords), 1))  # 复制到每个点
        
        # 归一化
        xyz_norm = self._normalize_data(xyz_coords, 'xyz')
        conditions_norm = self._normalize_data(conditions, 'conditions')
        
        # 转换为tensor
        xyz_tensor = torch.tensor(xyz_norm, dtype=torch.float32).to(self.device)
        conditions_tensor = torch.tensor(conditions_norm, dtype=torch.float32).to(self.device)
        
        # 批量预测
        htc_pred_list = []
        beta_pred_list = []
        
        batch_size = 1000  # 批处理大小
        num_batches = (len(xyz_coords) + batch_size - 1) // batch_size
        
        with torch.no_grad():
            for i in tqdm(range(num_batches), desc="预测进度"):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(xyz_coords))
                
                xyz_batch = xyz_tensor[start_idx:end_idx]
                conditions_batch = conditions_tensor[start_idx:end_idx]
                
                # 模型预测
                htc_norm, beta_norm = self.model(xyz_batch, conditions_batch)
                
                # 反归一化
                htc_batch, beta_batch = self._denormalize_predictions(
                    htc_norm.cpu().numpy(), beta_norm.cpu().numpy()
                )
                
                htc_pred_list.append(htc_batch)
                beta_pred_list.append(beta_batch)
        
        # 合并结果
        htc_pred = np.concatenate(htc_pred_list)
        beta_pred = np.concatenate(beta_pred_list)
        
        print(f"预测完成！")
        print(f"HTC预测范围: [{htc_pred.min():.2f}, {htc_pred.max():.2f}] W/m²·K")
        print(f"Beta预测范围: [{beta_pred.min():.4f}, {beta_pred.max():.4f}]")
        
        return htc_pred, beta_pred

    # 已移除参数范围检查 - 允许任意参数预测

    def save_results(self, xyz_coords, htc_pred, beta_pred, output_file):
        """
        保存预测结果到文件
        Args:
            xyz_coords: numpy array - xyz坐标
            htc_pred: numpy array - HTC预测值
            beta_pred: numpy array - Beta预测值
            output_file: str - 输出文件路径
        """
        print(f"\n正在保存结果到: {output_file}")

        # 创建结果数组
        results = np.column_stack([xyz_coords, beta_pred, htc_pred])

        # 保存到文件
        header = "x\ty\tz\tbeta\thtc"
        np.savetxt(output_file, results, delimiter='\t', header=header, comments='', fmt='%.6f')

        print(f"结果已保存！包含 {len(results)} 个点的预测结果")

    def plot_results(self, xyz_coords, htc_pred, beta_pred, save_path=None):
        """
        绘制预测结果
        Args:
            xyz_coords: numpy array - xyz坐标
            htc_pred: numpy array - HTC预测值
            beta_pred: numpy array - Beta预测值
            save_path: str - 图片保存路径
        """
        print("\n正在生成可视化图表...")

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # HTC沿x坐标分布
        axes[0, 0].scatter(xyz_coords[:, 0], htc_pred, alpha=0.6, s=20, c='red')
        axes[0, 0].set_xlabel('X Coordinate')
        axes[0, 0].set_ylabel('HTC (W/m²·K)')
        axes[0, 0].set_title('HTC Distribution along X')
        axes[0, 0].grid(True, alpha=0.3)

        # Beta沿x坐标分布
        axes[0, 1].scatter(xyz_coords[:, 0], beta_pred, alpha=0.6, s=20, c='blue')
        axes[0, 1].set_xlabel('X Coordinate')
        axes[0, 1].set_ylabel('Beta')
        axes[0, 1].set_title('Beta Distribution along X')
        axes[0, 1].grid(True, alpha=0.3)

        # HTC分布直方图
        axes[1, 0].hist(htc_pred, bins=30, alpha=0.7, color='red', edgecolor='black')
        axes[1, 0].set_xlabel('HTC (W/m²·K)')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].set_title(f'HTC Distribution (Mean: {htc_pred.mean():.2f})')
        axes[1, 0].grid(True, alpha=0.3)

        # Beta分布直方图
        axes[1, 1].hist(beta_pred, bins=30, alpha=0.7, color='blue', edgecolor='black')
        axes[1, 1].set_xlabel('Beta')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].set_title(f'Beta Distribution (Mean: {beta_pred.mean():.4f})')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")
        else:
            plt.show()

        plt.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='自定义HTC和Beta预测')
    parser.add_argument('--model', type=str, default='checkpoints/best_model.pth',
                       help='模型检查点路径')
    parser.add_argument('--xyz_file', type=str, required=True,
                       help='xyz坐标文件路径')
    parser.add_argument('--velocity', type=float, required=True,
                       help='速度 (m/s)')
    parser.add_argument('--temperature', type=float, required=True,
                       help='温度 (°C)')
    parser.add_argument('--lwc', type=float, required=True,
                       help='液态水含量')
    parser.add_argument('--mvd', type=float, required=True,
                       help='平均体积直径 (μm)')
    parser.add_argument('--time', type=float, default=5.0,
                       help='时间步 (0-9对应1-10分钟，默认5对应6分钟)')
    parser.add_argument('--output', type=str, default='prediction_results.txt',
                       help='输出文件路径')
    parser.add_argument('--plot', type=str, default='prediction_plot.png',
                       help='图表保存路径')

    args = parser.parse_args()

    try:
        # 创建预测器
        predictor = CustomHTCBetaPredictor(args.model)

        # 加载坐标
        xyz_coords = predictor.load_xyz_coordinates(args.xyz_file)

        # 进行预测
        htc_pred, beta_pred = predictor.predict_points(
            xyz_coords, args.velocity, args.temperature,
            args.lwc, args.mvd, args.time
        )

        # 保存结果
        predictor.save_results(xyz_coords, htc_pred, beta_pred, args.output)

        # 绘制图表
        predictor.plot_results(xyz_coords, htc_pred, beta_pred, args.plot)

        # 打印统计信息
        print(f"\n=== 预测结果统计 ===")
        print(f"点数: {len(xyz_coords)}")
        print(f"HTC统计: 最小={htc_pred.min():.2f}, 最大={htc_pred.max():.2f}, 平均={htc_pred.mean():.2f} W/m²·K")
        print(f"Beta统计: 最小={beta_pred.min():.4f}, 最大={beta_pred.max():.4f}, 平均={beta_pred.mean():.4f}")
        print(f"结果已保存到: {args.output}")
        print(f"图表已保存到: {args.plot}")

    except Exception as e:
        print(f"预测过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
