#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的模型测试脚本
直接修改文件中的参数即可使用
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import torch
from inference import IceShapePredictor

# ==================== 配置参数 ====================
# 修改这些参数来测试不同的情况

# 文件路径 - 修改为你的文件路径
INITIAL_FILE = "../data/iceshape-predict.txt"  # 初始冰形文件

# 工况参数 - 修改为你的测试工况
CONDITIONS = {
    'Height': 0.0,           # 高度 (m)
    'Velocity': 128.0,       # 速度 (m/s)  
    'Temperature': 253.0,    # 温度 (K)
    'LWC': 0.43,            # 液态水含量
    'MVD': 35.0,            # 平均体积直径
    'AOA': 0.0              # 攻角 (度)
}

# 预测时间
TIME_DELTA = 1.0  # 预测时间差 (分钟)

# 模型文件路径
MODEL_PATH = "checkpoints/best_model.pth"
SCALER_PATH = "checkpoints/scalers.pkl"

# 输出设置
SAVE_RESULT = True                    # 是否保存预测结果
OUTPUT_FILE = "prediction_result.txt" # 预测结果保存文件名
SHOW_PLOT = True                      # 是否显示图片
SAVE_PLOT = True                      # 是否保存图片
PLOT_FILE = "prediction_comparison.png" # 图片保存文件名

# ==================== 主要函数 ====================

def load_data():
    """加载数据"""
    print("=== 加载数据 ===")

    # 加载初始冰形
    if not os.path.exists(INITIAL_FILE):
        raise FileNotFoundError(f"找不到初始冰形文件: {INITIAL_FILE}")

    initial_shape = np.loadtxt(INITIAL_FILE)
    print(f"初始冰形: {initial_shape.shape[0]}点")
    print(f"  X范围: [{initial_shape[:,0].min():.6f}, {initial_shape[:,0].max():.6f}]")
    print(f"  Y范围: [{initial_shape[:,1].min():.6f}, {initial_shape[:,1].max():.6f}]")

    return initial_shape


def prepare_conditions():
    """准备工况参数"""
    print("\n=== 工况参数 ===")
    
    # 转换温度：K → °C
    temp_celsius = CONDITIONS['Temperature'] - 273.15
    
    # 按照模型期望的顺序组织参数 [H, V, T, LWC, MVD, AOA]
    model_conditions = np.array([
        CONDITIONS['Height'],
        CONDITIONS['Velocity'], 
        temp_celsius,           # 转换为摄氏度
        CONDITIONS['LWC'],
        CONDITIONS['MVD'],
        CONDITIONS['AOA']
    ], dtype=np.float32)
    
    print(f"高度: {CONDITIONS['Height']} m")
    print(f"速度: {CONDITIONS['Velocity']} m/s") 
    print(f"温度: {temp_celsius:.1f} °C ({CONDITIONS['Temperature']} K)")
    print(f"LWC: {CONDITIONS['LWC']}")
    print(f"MVD: {CONDITIONS['MVD']}")
    print(f"攻角: {CONDITIONS['AOA']} °")
    print(f"预测时间: {TIME_DELTA} 分钟")
    
    return model_conditions


def run_prediction(initial_shape, conditions):
    """运行预测"""
    print("\n=== 运行预测 ===")
    
    # 检查模型文件
    if not os.path.exists(MODEL_PATH):
        raise FileNotFoundError(f"找不到模型文件: {MODEL_PATH}")
    
    # 加载模型
    print(f"加载模型: {MODEL_PATH}")
    predictor = IceShapePredictor(MODEL_PATH, None, SCALER_PATH)
    
    # 进行预测
    print("开始预测...")
    predicted_shape = predictor.predict_ice_shape(initial_shape, conditions, TIME_DELTA)
    
    print(f"预测完成！")
    print(f"预测结果形状: {predicted_shape.shape}")
    print(f"预测X范围: [{predicted_shape[:,0].min():.6f}, {predicted_shape[:,0].max():.6f}]")
    print(f"预测Y范围: [{predicted_shape[:,1].min():.6f}, {predicted_shape[:,1].max():.6f}]")
    
    return predicted_shape


def save_results(predicted_shape):
    """保存预测结果"""
    if SAVE_RESULT:
        print(f"\n=== 保存结果 ===")
        # 只保存XY坐标，不要Z坐标
        if predicted_shape.shape[1] >= 2:
            result_to_save = predicted_shape[:, :2]  # 只取前两列(X,Y)
        else:
            result_to_save = predicted_shape

        np.savetxt(OUTPUT_FILE, result_to_save, fmt="%.6f", delimiter="\t")
        print(f"预测结果已保存到: {OUTPUT_FILE} (只包含XY坐标)")


def visualize_results(initial_shape, predicted_shape):
    """可视化结果"""
    print(f"\n=== 生成可视化 ===")

    # 创建图形 - 两个子图：完整对比 + 前缘细节
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 主图：初始 vs 预测对比
    ax1.plot(initial_shape[:, 0], initial_shape[:, 1], 'g-', linewidth=2, label='Initial', alpha=0.8)
    ax1.plot(predicted_shape[:, 0], predicted_shape[:, 1], 'r--', linewidth=2, label='Predicted', alpha=0.8)

    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_title('Complete Ice Shape Prediction')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')

    # 前缘细节 (x < 0.2)
    mask_init = initial_shape[:, 0] < 0.2
    mask_pred = predicted_shape[:, 0] < 0.2

    ax2.plot(initial_shape[mask_init, 0], initial_shape[mask_init, 1], 'g-', linewidth=2, label='Initial', alpha=0.8)
    ax2.plot(predicted_shape[mask_pred, 0], predicted_shape[mask_pred, 1], 'r--', linewidth=2, label='Predicted', alpha=0.8)

    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_title('Leading Edge Detail (X < 0.2)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.axis('equal')

    # 添加总标题
    temp_celsius = CONDITIONS['Temperature'] - 273.15
    fig.suptitle(f'Ice Shape Prediction - {TIME_DELTA} min\n'
                f'V={CONDITIONS["Velocity"]:.0f}m/s, T={temp_celsius:.1f}°C, '
                f'LWC={CONDITIONS["LWC"]:.2f}, MVD={CONDITIONS["MVD"]:.0f}',
                fontsize=14)

    plt.tight_layout()

    # 保存图片
    if SAVE_PLOT:
        plt.savefig(PLOT_FILE, dpi=150, bbox_inches='tight')
        print(f"图片已保存到: {PLOT_FILE}")

    # 显示图片
    if SHOW_PLOT:
        plt.show()
    else:
        plt.close()





def main():
    """主函数"""
    try:
        print("=== 冰形预测测试 ===")

        # 1. 加载数据
        initial_shape = load_data()

        # 2. 准备工况参数
        conditions = prepare_conditions()

        # 3. 运行预测
        predicted_shape = run_prediction(initial_shape, conditions)

        # 4. 保存结果
        save_results(predicted_shape)

        # 5. 可视化结果
        visualize_results(initial_shape, predicted_shape)

        print("\n✅ 测试完成！")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
