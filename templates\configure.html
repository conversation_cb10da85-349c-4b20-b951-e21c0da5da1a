<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STL模型管理</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px 0;
        }
        .container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            text-align: center;
            width: 100%;
            max-width: 600px;
            transition: all 0.3s ease;
        }
        .container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(31, 38, 135, 0.5);
        }
        .page-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 32px;
            font-weight: bold;
        }
        .current-model {
            background: rgba(102, 126, 234, 0.1);
            border: 2px solid #667eea;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        .current-model h3 {
            margin: 0 0 15px 0;
            color: #2d3748;
            font-size: 20px;
            font-weight: 600;
        }
        .model-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        .model-details {
            flex: 1;
        }
        .model-name {
            font-size: 18px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 8px;
        }
        .model-path {
            font-size: 14px;
            color: #718096;
            font-family: 'Courier New', monospace;
            background: rgba(255, 255, 255, 0.7);
            padding: 8px 12px;
            border-radius: 8px;
            word-break: break-all;
        }
        .model-actions {
            display: flex;
            gap: 15px;
        }
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
        }
        .upload-section {
            background: rgba(255, 255, 255, 0.5);
            border: 2px dashed #667eea;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }
        .upload-section:hover {
            background: rgba(102, 126, 234, 0.05);
            border-color: #5a67d8;
        }
        .upload-section h3 {
            margin: 0 0 20px 0;
            color: #2d3748;
            font-size: 20px;
            font-weight: 600;
        }
        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
            margin-bottom: 20px;
        }
        .file-input {
            width: calc(100% - 40px);
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            cursor: pointer;
            box-sizing: border-box;
        }
        .file-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            padding: 15px 30px;
            font-size: 16px;
            width: 100%;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(72, 52, 212, 0.4);
            padding: 15px 30px;
            font-size: 16px;
            margin-top: 20px;
        }
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 52, 212, 0.6);
        }
        .status-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-weight: 600;
            display: none;
        }
        .status-success {
            background: rgba(72, 187, 120, 0.1);
            color: #2f855a;
            border: 1px solid rgba(72, 187, 120, 0.3);
        }
        .status-error {
            background: rgba(245, 101, 101, 0.1);
            color: #c53030;
            border: 1px solid rgba(245, 101, 101, 0.3);
        }
        footer {
            text-align: center;
            margin-top: 30px;
            font-size: 0.9em;
            color: #4a5568;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 0;
            width: 100%;
            display: none;
        }
        
        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 0 20px;
                padding: 30px 25px;
            }
            .model-info {
                flex-direction: column;
                align-items: stretch;
            }
            .model-actions {
                justify-content: center;
            }
            .page-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">STL模型管理</h1>
        
        <!-- 当前模型信息 -->
        <div class="current-model">
            <h3>🛩️ 当前STL模型</h3>
            <div class="model-info">
                <div class="model-details">
                    <div class="model-name" id="current-model-name">检测中...</div>
                    <div class="model-path" id="current-model-path">路径检测中...</div>
                </div>
                <div class="model-actions">
                    <button class="btn btn-danger" id="delete-btn" onclick="deleteCurrentModel()" disabled>
                        🗑️ 删除模型
                    </button>
                </div>
            </div>
        </div>

        <!-- 上传新模型 -->
        <div class="upload-section">
            <h3>📁 上传新的STL模型</h3>
            <form id="upload-form" enctype="multipart/form-data">
                <div class="file-input-wrapper">
                    <input type="file" class="file-input" id="stlFile" name="stlFile" accept=".stl" required>
                </div>
                <button type="submit" class="btn btn-primary">
                    ⬆️ 上传STL文件
                </button>
            </form>
        </div>

        <!-- 状态消息 -->
        <div id="status-message" class="status-message"></div>

        <!-- 返回按钮 -->
        <button class="btn btn-secondary" onclick="location.href='/display'">
            ↩️ 返回主页面
        </button>
    </div>

    <script>
        // 检查当前STL模型
        function checkCurrentModel() {
            fetch('/current-stl')
                .then(response => response.json())
                .then(data => {
                    const nameElement = document.getElementById('current-model-name');
                    const pathElement = document.getElementById('current-model-path');
                    const deleteBtn = document.getElementById('delete-btn');
                    
                    if (data.exists) {
                        nameElement.textContent = data.fileName || 'naca0012.stl';
                        pathElement.textContent = `路径: ${data.filePath || '/static/naca0012.stl'} | 大小: ${(data.fileSize / 1024).toFixed(2)} KB`;
                        deleteBtn.disabled = false;
                        deleteBtn.style.opacity = '1';
                        deleteBtn.style.cursor = 'pointer';
                    } else {
                        nameElement.textContent = '暂无STL模型';
                        pathElement.textContent = '请上传STL文件以在前端显示3D模型';
                        deleteBtn.disabled = true;
                        deleteBtn.style.opacity = '0.5';
                        deleteBtn.style.cursor = 'not-allowed';
                    }
                })
                .catch(error => {
                    console.error('检查STL模型失败:', error);
                    document.getElementById('current-model-name').textContent = '检测失败';
                    document.getElementById('current-model-path').textContent = '无法连接到服务器，请检查网络连接';
                    document.getElementById('delete-btn').disabled = true;
                });
        }

        // 删除当前模型
        function deleteCurrentModel() {
            if (confirm('⚠️ 确定要删除当前的STL模型吗？\n\n删除后：\n• 前端将无法显示3D模型\n• 需要重新上传STL文件才能恢复显示\n\n此操作不可撤销！')) {
                // 显示加载状态
                const deleteBtn = document.getElementById('delete-btn');
                const originalText = deleteBtn.innerHTML;
                deleteBtn.innerHTML = '🔄 删除中...';
                deleteBtn.disabled = true;
                
                fetch('/delete-stl', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showStatus('✅ STL模型删除成功！前端3D显示已清空。', 'success');
                        checkCurrentModel(); // 刷新显示
                    } else {
                        showStatus('❌ 删除失败：' + (data.error || '未知错误'), 'error');
                        deleteBtn.innerHTML = originalText;
                        deleteBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('删除STL模型失败:', error);
                    showStatus('❌ 删除失败：网络连接错误', 'error');
                    deleteBtn.innerHTML = originalText;
                    deleteBtn.disabled = false;
                });
            }
        }

        // 上传STL文件
        document.getElementById('upload-form').addEventListener('submit', function(event) {
            event.preventDefault();

            const formData = new FormData(this);
            const fileInput = document.getElementById('stlFile');
            const submitBtn = event.target.querySelector('button[type="submit"]');
            
            if (!fileInput.files[0]) {
                showStatus('❌ 请选择要上传的STL文件', 'error');
                return;
            }

            // 检查文件扩展名
            const fileName = fileInput.files[0].name;
            if (!fileName.toLowerCase().endsWith('.stl')) {
                showStatus('❌ 请上传.stl格式的文件', 'error');
                return;
            }

            // 检查文件大小（限制为50MB）
            const fileSize = fileInput.files[0].size;
            if (fileSize > 50 * 1024 * 1024) {
                showStatus('❌ 文件过大，请上传小于50MB的STL文件', 'error');
                return;
            }

            // 显示上传进度
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '⬆️ 上传中...';
            submitBtn.disabled = true;

            fetch('/upload-stl', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus(`✅ STL文件上传成功！\n文件名：${data.fileName}\n大小：${(data.fileSize / 1024).toFixed(2)} KB\n前端3D模型已更新。`, 'success');
                    checkCurrentModel(); // 刷新显示
                    document.getElementById('upload-form').reset(); // 清空表单
                } else {
                    showStatus('❌ 上传失败：' + (data.error || '未知错误'), 'error');
                }
                
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            })
            .catch(error => {
                console.error('上传STL文件失败:', error);
                showStatus('❌ 上传失败：网络连接错误', 'error');
                
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // 显示状态消息
        function showStatus(message, type) {
            const statusElement = document.getElementById('status-message');
            // 将换行符转换为HTML换行
            const htmlMessage = message.replace(/\n/g, '<br>');
            statusElement.innerHTML = htmlMessage;
            statusElement.className = `status-message status-${type}`;
            statusElement.style.display = 'block';
            
            // 5秒后自动隐藏
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 5000);
        }

        // 页面加载时检查当前模型
        checkCurrentModel();
    </script>
</body>
</html>
