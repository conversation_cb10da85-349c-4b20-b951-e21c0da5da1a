import os
from flask import Flask, render_template, request, jsonify, redirect, url_for, session, Response
import json

# 添加结冰冰形预测相关导入
import sys
try:
    import torch
    import numpy as np
    # 不在这里导入PhysEvolveNet，在使用时动态导入
    ICE_SHAPE_AVAILABLE = True
except ImportError:
    ICE_SHAPE_AVAILABLE = False
    print("警告: 结冰冰形预测模型未安装或导入失败")

# 添加气动系数预测相关导入
try:
    import sys
    import os
    # 添加ice-num目录到路径
    ice_num_path = os.path.join(os.path.dirname(__file__), 'ice-num')
    if ice_num_path not in sys.path:
        sys.path.insert(0, ice_num_path)

    from aero_predict import predict_aerodynamic_coefficients  # type: ignore
    AERO_PREDICTION_AVAILABLE = True
except ImportError as e:
    AERO_PREDICTION_AVAILABLE = False
    print(f"警告: 气动系数预测模型未安装或导入失败: {e}")

# 添加HTC和Beta预测相关导入
try:
    # 添加ice-HTC目录到路径
    ice_htc_path = os.path.join(os.path.dirname(__file__), 'ice-HTC')
    if ice_htc_path not in sys.path:
        sys.path.insert(0, ice_htc_path)

    from custom_predict import CustomHTCBetaPredictor  # type: ignore
    HTC_BETA_PREDICTION_AVAILABLE = True
except ImportError as e:
    HTC_BETA_PREDICTION_AVAILABLE = False
    print(f"警告: HTC和Beta预测模型未安装或导入失败: {e}")

STL_FOLDER = 'static'
ALLOWED_STL_EXTENSIONS = {'stl'}

app = Flask(__name__)
app.secret_key = 'your_secret_key_here'  # 设置一个安全的密钥

if not os.path.exists(STL_FOLDER):
    os.makedirs(STL_FOLDER)

def allowed_stl_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_STL_EXTENSIONS

@app.route('/')
def home():
    return render_template('home.html')

@app.route('/login', methods=['POST'])
def login():
    username = request.form.get('username')
    password = request.form.get('password')
    if username == '3313637051' and password == 'aidi+6898':
        session['logged_in'] = True  # 设置会话状态为已登�?
        return redirect(url_for('display'))
    else:
        return render_template('home.html', error='账号或密码错误，请重试！')

@app.route('/logout')
def logout():
    session.pop('logged_in', None)  # 清除会话状�?
    return redirect(url_for('home'))

# 添加一个装饰器函数，用于检查登录状�?
def login_required(f):
    def wrapper(*args, **kwargs):
        if not session.get('logged_in'):
            return redirect(url_for('home'))
        return f(*args, **kwargs)
    wrapper.__name__ = f.__name__
    return wrapper

@app.route('/monitoring')
def monitoring():
    return render_template('index.html', title='平行实验实时监控界面')

@app.route('/display')
@login_required  # 保护路由，未登录用户无法访问
def display():
    # 直接返回模板，不再依赖.dat文件
    return render_template('index.html', title='平行实验展示模块')

@app.route('/configure')
@login_required  # 保护路由，未登录用户无法访问

def configure():
    return render_template('configure.html', title='平行实验配置模块')

@app.route('/predict', methods=['POST'])
def predict():
    # 获取工况参数
    data = request.json
    lwc = data.get('lwc', 0)
    mvd = data.get('mvd', 0)
    angle_of_attack = data.get('angle_of_attack', 0)
    # 其他工况参数可以继续添加

    # 模拟预测结果
    results = {
        "water_collection": 0.85,  # 示例�?
        "heat_transfer": 1.23,    # 示例�?
        "aerodynamic": 0.67,     # 示例�?
        "icing_shape": "复杂冰形"  # 示例�?
    }

    return jsonify(results)

@app.route('/details/water_collection')
def details_water_collection():
    return render_template('details.html', title='水滴收集系数', result='水滴收集系数的详细结果')

@app.route('/details/heat_transfer')
def details_heat_transfer():
    return render_template('details.html', title='对流换热系数', result='对流换热系数的详细结果')

@app.route('/details/aerodynamic')
def details_aerodynamic():
    return render_template('details.html', title='气动系数', result='气动系数的详细结果')

@app.route('/details/icing_shape')
def details_icing_shape():
    """渲染结冰冰形页面并传递工况信息"""
    conditions = session.get('conditions', {
        "Velocity": 100,
        "Temperature": -5,
        "LWC": 0.5,
        "MVD": 20
    })
    return render_template('icing_shape_details.html', title='结冰冰形详细结果', conditions=conditions)

@app.route('/details/icing_shape_details')
def icing_shape_details():
    return render_template('icing_shape_details.html')

@app.route('/save_configuration', methods=['POST'])
def save_configuration():
    data = request.json
    model = data.get('model')
    param1 = data.get('param1')
    param2 = data.get('param2')

    # 模拟保存逻辑，可以替换为实际数据库存�?
    print(f"保存的配�? 模型={model}, 参数1={param1}, 参数2={param2}")

    return jsonify({"message": "配置已保存"}), 200

@app.route('/load_model', methods=['POST'])
def load_model():
    model = request.form.get('model')

    # 模拟装载逻辑，可以替换为实际模型加载代码
    print(f"装载的模�? {model}")

    return jsonify({"message": f"模型 {model} 已成功装载"}), 200

@app.route('/predict_htc_beta', methods=['POST'])
def predict_htc_beta_route():
    """处理HTC和Beta预测请求"""
    try:
        if not HTC_BETA_PREDICTION_AVAILABLE:
            return jsonify({"success": False, "error": "HTC和Beta预测模型不可用"})

        # 获取 JSON 数据
        data = request.json
        velocity = float(data.get('Velocity', 100))  # 速度 m/s
        temperature_k = float(data.get('Temperature', 273))  # 开尔文温度
        lwc = float(data.get('LWC', 0.5))
        mvd = float(data.get('MVD', 20))

        # 温度转换：开尔文 → 摄氏度
        temperature_c = temperature_k - 273.15

        # 默认时间为1分钟（对应time_step=0，因为0-9对应1-10分钟）
        time_step = 0  # 1分钟

        print(f"HTC和Beta预测 - 速度: {velocity}m/s, 温度: {temperature_k}K ({temperature_c:.1f}°C), LWC: {lwc}, MVD: {mvd}, 时间: {time_step+1}分钟")

        # 创建预测器
        model_path = 'ice-HTC/checkpoints/best_model.pth'
        if not os.path.exists(model_path):
            return jsonify({"success": False, "error": "HTC和Beta模型文件不存在"})

        predictor = CustomHTCBetaPredictor(model_path)

        # 加载坐标文件
        xyz_file = 'data/iceshape-predict.txt'
        if not os.path.exists(xyz_file):
            return jsonify({"success": False, "error": "坐标文件不存在"})

        xyz_coords = predictor.load_xyz_coordinates(xyz_file)

        # 进行预测
        htc_pred, beta_pred = predictor.predict_points(
            xyz_coords, velocity, temperature_c, lwc, mvd, time_step
        )

        # 计算统计信息
        htc_stats = {
            "min": float(htc_pred.min()),
            "max": float(htc_pred.max()),
            "mean": float(htc_pred.mean()),
            "std": float(htc_pred.std())
        }

        beta_stats = {
            "min": float(beta_pred.min()),
            "max": float(beta_pred.max()),
            "mean": float(beta_pred.mean()),
            "std": float(beta_pred.std())
        }

        # 准备返回数据
        result = {
            "success": True,
            "htc": {
                "values": htc_pred.tolist(),
                "stats": htc_stats,
                "unit": "W/m²·K"
            },
            "beta": {
                "values": beta_pred.tolist(),
                "stats": beta_stats,
                "unit": "无量纲"
            },
            "coordinates": xyz_coords.tolist(),
            "conditions": {
                "velocity": velocity,
                "temperature_k": temperature_k,
                "temperature_c": temperature_c,
                "lwc": lwc,
                "mvd": mvd,
                "time_minutes": time_step + 1
            }
        }

        return jsonify(result)

    except Exception as e:
        print(f"HTC和Beta预测失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "error": f"预测失败: {str(e)}"})

@app.route('/predict_aero_coefficients', methods=['POST'])
def predict_aero_coefficients_route():
    """处理气动系数预测请求"""
    try:
        if not AERO_PREDICTION_AVAILABLE:
            return jsonify({"success": False, "error": "气动系数预测模型不可用"})

        # 获取 JSON 数据
        data = request.json
        temperature_k = float(data.get('Temperature', 273))  # 开尔文温度
        lwc = float(data.get('LWC', 0.5))
        mvd = float(data.get('MVD', 20))

        # 温度转换：开尔文 → 摄氏度
        temperature_c = temperature_k - 273.15

        print(f"气动系数预测 - 温度: {temperature_k}K ({temperature_c:.1f}°C), LWC: {lwc}, MVD: {mvd}")

        # 使用冰形文件进行预测
        iceshape_path = "data/iceshape-predict.txt"
        result = predict_aerodynamic_coefficients(iceshape_path, lwc, mvd, temperature_c)

        if result["success"]:
            # 加载干净构型数据
            try:
                clear_data = np.loadtxt("ice-num/clear.txt")
                # 假设攻角与预测结果的攻角一致
                clean_aoa = result["aoa"]  # 使用相同的攻角
                clean_lift = clear_data[:len(clean_aoa), 0].tolist()  # 升力系数
                clean_drag = clear_data[:len(clean_aoa), 1].tolist()  # 阻力系数

                result["clean_lift_coefficient"] = clean_lift
                result["clean_drag_coefficient"] = clean_drag
                result["clean_aoa"] = clean_aoa

            except Exception as e:
                print(f"警告: 无法加载干净构型数据: {str(e)}")
                # 如果加载失败，使用默认值
                result["clean_lift_coefficient"] = []
                result["clean_drag_coefficient"] = []
                result["clean_aoa"] = []

        return jsonify(result)

    except Exception as e:
        print(f"气动系数预测失败: {str(e)}")
        return jsonify({"success": False, "error": f"预测失败: {str(e)}"})

# 添加结冰冰形预测辅助函数
def load_airfoil_2d(filepath):
    try:
        data = np.loadtxt(filepath)
        return data if data.ndim == 2 and data.shape[1] == 2 else None
    except:
        return None

def load_airfoil_3d(filepath):
    try:
        data = np.loadtxt(filepath)
        return data if data.ndim == 2 and data.shape[1] == 3 else None
    except:
        return None

def normalize_conditions(cond_data, stats):
    min_val = np.array(stats['conditions']['min'])
    max_val = np.array(stats['conditions']['max'])
    denominator = max_val - min_val
    denominator[denominator == 0] = 1e-9
    return (cond_data - min_val) / denominator

def predict_ice_shape(velocity, temperature, lwc, mvd, delta_t, height=0.0, aoa=0.0):
    """预测结冰冰形 - 使用新的ice-shape-new模型"""
    if not ICE_SHAPE_AVAILABLE:
        return {"error": "结冰冰形预测模型不可用"}

    try:
        # 设置新模型路径
        model_path = 'ice-shape-new/checkpoints/best_model.pth'
        scaler_path = 'ice-shape-new/checkpoints/scalers.pkl'
        initial_shape_path = 'data/iceshape-predict.txt'
        clean_body_path = 'data/body.txt'
        experimental_shape_path = 'data/iceshape-ture.txt'

        # 检查文件是否存在
        for path in [model_path, initial_shape_path, clean_body_path]:
            if not os.path.exists(path):
                return {"error": f"找不到必要文件: {path}"}

        # 设置环境变量避免OpenMP冲突
        os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

        # 动态导入新模型
        ice_shape_new_path = os.path.join(os.path.dirname(__file__), 'ice-shape-new')
        if ice_shape_new_path not in sys.path:
            sys.path.insert(0, ice_shape_new_path)

        # 使用importlib动态导入，避免IDE警告
        import importlib.util
        spec = importlib.util.spec_from_file_location("inference", os.path.join(ice_shape_new_path, "inference.py"))
        inference_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(inference_module)
        IceShapePredictor = inference_module.IceShapePredictor

        # 加载几何数据
        clean_body_2d = load_airfoil_2d(clean_body_path)
        initial_shape_3d = load_airfoil_3d(initial_shape_path)

        if clean_body_2d is None or initial_shape_3d is None:
            return {"error": "无法加载几何数据"}

        # 加载实验冰形数据用于前端显示（可选）
        experimental_shape_path = 'data/iceshape-ture.txt'
        experimental_shape_3d = load_airfoil_3d(experimental_shape_path)
        if experimental_shape_3d is None:
            # 如果没有实验数据，使用初始形状作为参考
            experimental_shape_coords = initial_shape_3d[:, 0:2]
        else:
            experimental_shape_coords = experimental_shape_3d[:, 0:2]

        # 创建预测器（如果scaler文件不存在，传入None）
        scaler_file = scaler_path if os.path.exists(scaler_path) else None
        predictor = IceShapePredictor(model_path, None, scaler_file)

        # 准备工况参数 [Height, Velocity, Temperature(°C), LWC, MVD, AOA]
        # 温度转换：开尔文 → 摄氏度（与其他预测保持一致）
        temperature_celsius = temperature - 273.15
        conditions = np.array([
            height,
            velocity,
            temperature_celsius,
            lwc,
            mvd,
            aoa
        ], dtype=np.float32)

        # 执行预测
        print(f"🔍 预测参数: 时间={delta_t}分钟, 条件={conditions}")
        predicted_shape = predictor.predict_ice_shape(initial_shape_3d, conditions, delta_t)
        print(f"📊 预测结果形状: {predicted_shape.shape if hasattr(predicted_shape, 'shape') else type(predicted_shape)}")

        # 确保封闭轮廓
        predicted_shape_coords = np.vstack([predicted_shape, predicted_shape[0]])
        experimental_shape_coords = np.vstack([experimental_shape_coords, experimental_shape_coords[0]])
        clean_body_2d = np.vstack([clean_body_2d, clean_body_2d[0]])

        # 返回预测结果
        return {
            "success": True,
            "predicted_shape": predicted_shape_coords.tolist(),
            "initial_shape": experimental_shape_coords.tolist(),  # 返回真正的实验数据
            "clean_body": clean_body_2d.tolist()
        }

    except Exception as e:
        import traceback
        traceback.print_exc()
        return {"error": f"预测过程中发生错误: {str(e)}"}

@app.route('/predict_ice_shape', methods=['POST'])
def predict_ice_shape_route():
    """处理结冰冰形预测请求"""
    try:
        # 获取 JSON 数据
        data = request.json
        velocity = float(data.get('Velocity', 0))
        temperature = float(data.get('Temperature', 0))
        lwc = float(data.get('LWC', 0))
        mvd = float(data.get('MVD', 0))
        height = float(data.get('Height', 0.0))  # 新增高度参数
        aoa = float(data.get('AOA', 0.0))        # 新增攻角参数
        delta_t = 1  # 修改为1分钟，与测试代码保持一致

        # 执行预测
        result = predict_ice_shape(velocity, temperature, lwc, mvd, delta_t, height, aoa)

        return jsonify(result)

    except Exception as e:
        return jsonify({"error": f"预测请求处理失败: {str(e)}"})

@app.route('/get_icing_shape')
def get_icing_shape():
    """根据时间点返回结冰冰形预测数据"""
    try:
        # 获取URL参数，优先使用URL参数，其次使用session中的值
        time = request.args.get('time', default=60, type=int)
        velocity = request.args.get('velocity', type=float)
        temperature = request.args.get('temperature', type=float)
        lwc = request.args.get('lwc', type=float)
        mvd = request.args.get('mvd', type=float)
        height = request.args.get('height', default=0.0, type=float)
        aoa = request.args.get('aoa', default=0.0, type=float)

        # 从会话中获取默认工况（作为备用）
        session_conditions = session.get('conditions', {
            "Velocity": 100,
            "Temperature": -5,
            "LWC": 0.5,
            "MVD": 20,
            "Height": 0.0,
            "AOA": 0.0
        })

        # 使用URL参数，如果没有则使用session中的值
        final_velocity = velocity if velocity is not None else session_conditions['Velocity']
        final_temperature = temperature if temperature is not None else session_conditions['Temperature']
        final_lwc = lwc if lwc is not None else session_conditions['LWC']
        final_mvd = mvd if mvd is not None else session_conditions['MVD']
        final_height = height if height is not None else session_conditions.get('Height', 0.0)
        final_aoa = aoa if aoa is not None else session_conditions.get('AOA', 0.0)

        print(f"🔍 get_icing_shape接收参数:")
        print(f"   URL参数: velocity={velocity}, temperature={temperature}, lwc={lwc}, mvd={mvd}")
        print(f"   Session参数: {session_conditions}")
        print(f"   最终使用: velocity={final_velocity}, temperature={final_temperature}, lwc={final_lwc}, mvd={final_mvd}, height={final_height}, aoa={final_aoa}")

        # 调用预测函数
        result = predict_ice_shape(
            final_velocity,
            final_temperature,
            final_lwc,
            final_mvd,
            time,
            final_height,
            final_aoa
        )

        if 'error' in result:
            return jsonify({"success": False, "error": result['error']}), 400

        return jsonify({
            "success": True,
            "predicted_shape": result['predicted_shape'],
            "clean_body": result['clean_body']
        })

    except Exception as e:
        return jsonify({"success": False, "error": f"处理请求时发生错误: {str(e)}"}), 500

@app.route('/set_conditions', methods=['POST'])
def set_conditions():
    """设置主页面的工况并存储到会话中"""
    data = request.json
    session['conditions'] = {
        "Velocity": data.get('Velocity', 100),
        "Temperature": data.get('Temperature', -5),
        "LWC": data.get('LWC', 0.5),
        "MVD": data.get('MVD', 20),
        "Height": data.get('Height', 0.0),
        "AOA": data.get('AOA', 0.0)
    }
    return jsonify({"success": True})

# STL模型管理相关路由
@app.route('/current-stl', methods=['GET'])
@login_required
def get_current_stl():
    """获取当前STL模型信息"""
    try:
        stl_path = os.path.join(STL_FOLDER, 'naca0012.stl')
        
        if os.path.exists(stl_path):
            file_size = os.path.getsize(stl_path)
            return jsonify({
                'exists': True,
                'fileName': 'naca0012.stl',
                'filePath': '/static/naca0012.stl',
                'fileSize': file_size
            })
        else:
            return jsonify({
                'exists': False,
                'fileName': None,
                'filePath': None,
                'fileSize': 0
            })
    except Exception as e:
        return jsonify({
            'exists': False,
            'error': str(e)
        }), 500

@app.route('/delete-stl', methods=['POST'])
@login_required
def delete_stl():
    """删除当前STL模型"""
    try:
        stl_path = os.path.join(STL_FOLDER, 'naca0012.stl')
        
        if os.path.exists(stl_path):
            os.remove(stl_path)
            return jsonify({
                'success': True,
                'message': 'STL模型删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'STL文件不存在'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'删除失败: {str(e)}'
        }), 500

@app.route('/upload-stl', methods=['POST'])
@login_required
def upload_stl():
    """上传新的STL模型"""
    try:
        if 'stlFile' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有文件被上传'
            }), 400

        file = request.files['stlFile']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '未选择文件'
            }), 400

        if file and allowed_stl_file(file.filename):
            # 确保static目录存在
            if not os.path.exists(STL_FOLDER):
                os.makedirs(STL_FOLDER)
            
            # 保存为naca0012.stl（替换现有文件）
            stl_path = os.path.join(STL_FOLDER, 'naca0012.stl')
            
            # 如果存在旧文件，先删除
            if os.path.exists(stl_path):
                os.remove(stl_path)
            
            # 保存新文件
            file.save(stl_path)
            
            # 验证文件是否保存成功
            if os.path.exists(stl_path):
                file_size = os.path.getsize(stl_path)
                return jsonify({
                    'success': True,
                    'message': 'STL文件上传成功',
                    'fileName': 'naca0012.stl',
                    'filePath': '/static/naca0012.stl',
                    'fileSize': file_size
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '文件保存失败'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': '文件类型不支持，请上传.stl格式的文件'
            }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'上传失败: {str(e)}'
        }), 500

@app.route('/favicon.ico')
def favicon():
    """返回空的favicon，避免404错误"""
    return Response('', mimetype='image/x-icon')

if __name__ == '__main__':
    app.run(debug=True)