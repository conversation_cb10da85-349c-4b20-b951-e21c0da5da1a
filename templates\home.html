<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平行实验平台</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            text-align: center;
            width: 100%;
            max-width: 420px;
            transition: all 0.3s ease;
        }
        .container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(31, 38, 135, 0.5);
        }
        h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: #2d3748;
            margin-bottom: 25px;
            font-size: 32px;
            font-weight: bold;
        }
        p {
            margin: 15px 0 25px;
            color: #4a5568;
            font-size: 16px;
            font-weight: 500;
        }
        form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        input {
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            width: 100%;
            font-size: 16px;
            box-sizing: border-box;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 1);
        }
        input::placeholder {
            color: #a0aec0;
            font-weight: 500;
        }
        button {
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
        button:active {
            transform: translateY(0);
        }
        .error {
            color: #e53e3e;
            margin-top: 15px;
            font-size: 14px;
            font-weight: 600;
            padding: 10px;
            background: rgba(254, 226, 226, 0.8);
            border: 1px solid rgba(245, 101, 101, 0.3);
            border-radius: 8px;
            backdrop-filter: blur(5px);
        }
        footer {
            text-align: center;
            margin-top: 30px;
            font-size: 0.9em;
            color: #4a5568;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 0;
            width: 100%;
            position: fixed;
            bottom: 0;
        }
        
        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                margin: 0 20px;
                padding: 30px 25px;
            }
            h1 {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>平行实验平台</h1>
        <p>欢迎使用平行实验平台，请登录以继续。</p>
        <form action="/login" method="POST">
            <input type="text" name="username" placeholder="账号" required>
            <input type="password" name="password" placeholder="密码" required>
            <button type="submit">登录</button>
        </form>
        {% if error %}
        <p class="error">{{ error }}</p>
        {% endif %}
    </div>
    <footer>
        版权归属一部五室@2025
    </footer>
</body>
</html>
