#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MVD对比测试脚本 - 对比MVD 20 vs 35的影响
"""

import os
import numpy as np

# 解决OpenMP库冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 设置matplotlib后端，避免OpenMP冲突
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import torch
from inference import IceShapePredictor

# ==================== 配置参数 ====================

# MVD对比条件
MVD_CONDITIONS = [
    {
        'name': 'MVD_20',
        'Temperature': 256.0,  # 256K
        'Height': 0.0,         # 0 m
        'Velocity': 80.0,      # 80 m/s
        'LWC': 0.2,           # 0.2 g/m³
        'MVD': 20.0,          # 20 μm
        'AOA': 0.0            # 0°
    },
    {
        'name': 'MVD_35',
        'Temperature': 256.0,  # 256K
        'Height': 0.0,         # 0 m
        'Velocity': 80.0,      # 80 m/s
        'LWC': 0.2,           # 0.2 g/m³
        'MVD': 35.0,          # 35 μm
        'AOA': 0.0            # 0°
    }
]

# 预测时间（只测试1分钟）
TIME_DELTA = 1.0  # 1分钟

# 模型和数据路径
MODEL_PATH = "checkpoints/best_model.pth"
INITIAL_SHAPE_PATH = "../test/iceshape-predict.txt"

# 是否保存图片
SAVE_PLOT = True

# ==================== 函数定义 ====================

def load_initial_shape():
    """加载初始冰形数据"""
    print("=== 加载初始冰形数据 ===")

    # 尝试不同的文件路径
    possible_paths = [
        INITIAL_SHAPE_PATH,
        "test/iceshape-predict.txt",
        "../test/iceshape-predict.txt"
    ]

    for path in possible_paths:
        if os.path.exists(path):
            print(f"找到初始冰形文件: {path}")
            data = np.loadtxt(path)

            # 检查数据格式并转换为[N, 3]格式
            if data.shape[1] >= 3:
                # 已经是3列格式
                initial_shape = data[:, :3]
            elif data.shape[1] == 2:
                # 只有X,Y坐标，添加Z坐标（设为0）
                initial_shape = np.column_stack([data, np.zeros(data.shape[0])])
            else:
                raise ValueError(f"数据格式不正确: {data.shape}")

            print(f"初始冰形: {initial_shape.shape[0]}点")
            print(f"  X范围: [{np.min(initial_shape[:, 0]):.6f}, {np.max(initial_shape[:, 0]):.6f}]")
            print(f"  Y范围: [{np.min(initial_shape[:, 1]):.6f}, {np.max(initial_shape[:, 1]):.6f}]")
            return initial_shape

    raise FileNotFoundError(f"找不到初始冰形文件，尝试了: {possible_paths}")

def prepare_conditions(condition_dict):
    """准备工况参数"""
    temp_celsius = condition_dict['Temperature'] - 273.15
    print(f"\n=== 工况参数 ({condition_dict['name']}) ===")
    print(f"高度: {condition_dict['Height']} m")
    print(f"速度: {condition_dict['Velocity']} m/s")
    print(f"温度: {temp_celsius:.1f} °C ({condition_dict['Temperature']} K)")
    print(f"LWC: {condition_dict['LWC']}")
    print(f"MVD: {condition_dict['MVD']}")
    print(f"攻角: {condition_dict['AOA']} °")
    print(f"预测时间: {TIME_DELTA} 分钟")

    # 返回numpy数组格式 [H, V, T, LWC, MVD, AOA]
    return np.array([
        condition_dict['Height'],
        condition_dict['Velocity'],
        condition_dict['Temperature'],
        condition_dict['LWC'],
        condition_dict['MVD'],
        condition_dict['AOA']
    ])

def run_prediction(initial_shape, conditions):
    """运行单次预测"""
    print(f"\n=== 运行预测 ===")
    
    # 初始化预测器
    predictor = IceShapePredictor(MODEL_PATH)
    
    print("开始预测...")
    predicted_shape = predictor.predict_ice_shape(
        initial_shape=initial_shape,
        conditions=conditions,
        time_delta=TIME_DELTA
    )
    
    print(f"  预测完成！形状: {predicted_shape.shape}")
    return predicted_shape

def save_result(predicted_shape, condition_name):
    """保存预测结果"""
    output_file = f"prediction_{condition_name}_{TIME_DELTA:.0f}min.txt"
    np.savetxt(output_file, predicted_shape, fmt='%.6f')
    print(f"  预测结果已保存到: {output_file}")

def visualize_mvd_comparison(initial_shape, results):
    """可视化MVD对比结果"""
    print(f"\n=== 生成MVD对比可视化 ===")
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 绘制初始形状
    ax.plot(initial_shape[:, 0], initial_shape[:, 1], 'k-', linewidth=3, 
            label='Initial (t=0)', alpha=0.8)
    
    # 绘制MVD=20结果
    if 'MVD_20' in results:
        ax.plot(results['MVD_20'][:, 0], results['MVD_20'][:, 1], 
                'b-', linewidth=3, label='MVD=20μm (1min)', alpha=0.8)
    
    # 绘制MVD=35结果  
    if 'MVD_35' in results:
        ax.plot(results['MVD_35'][:, 0], results['MVD_35'][:, 1], 
                'r-', linewidth=3, label='MVD=35μm (1min)', alpha=0.8)
    
    ax.set_xlabel('X', fontsize=14)
    ax.set_ylabel('Y', fontsize=14)
    ax.set_title('MVD Comparison: 20μm vs 35μm\n'
                'V=80m/s, T=256K(-17°C), LWC=0.2, t=1min', 
                fontsize=16, fontweight='bold')
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.axis('equal')
    
    plt.tight_layout()
    
    # 保存图片
    if SAVE_PLOT:
        plot_file = "mvd_comparison_20_vs_35.png"
        try:
            plt.savefig(plot_file, dpi=150, bbox_inches='tight', facecolor='white')
            print(f"MVD对比图已保存到: {plot_file}")
        except Exception as e:
            print(f"保存图片失败: {e}")
    
    # 关闭图形，释放内存
    plt.close('all')

# ==================== 主函数 ====================

def main():
    """主函数"""
    try:
        print("=== MVD对比测试: 20μm vs 35μm ===")
        
        # 1. 加载初始冰形数据
        initial_shape = load_initial_shape()
        
        # 2. 存储不同MVD的预测结果
        results = {}
        
        # 3. 对每个MVD条件进行预测
        for condition in MVD_CONDITIONS:
            condition_name = condition['name']
            print(f"\n{'='*50}")
            print(f"开始测试MVD条件: {condition_name}")
            print(f"{'='*50}")
            
            # 准备工况参数
            conditions = prepare_conditions(condition)
            
            # 运行预测
            predicted_shape = run_prediction(initial_shape, conditions)
            
            # 保存结果
            save_result(predicted_shape, condition_name)
            
            # 存储结果用于对比
            results[condition_name] = predicted_shape
        
        # 4. 生成MVD对比可视化
        visualize_mvd_comparison(initial_shape, results)
        
        print("\n🎉 MVD对比测试完成！")
        print("生成的文件:")
        print("  - mvd_comparison_20_vs_35.png: MVD对比图")
        print("  - prediction_MVD_20_1min.txt: MVD=20μm预测结果")
        print("  - prediction_MVD_35_1min.txt: MVD=35μm预测结果")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
