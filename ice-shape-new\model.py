#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新版PhysEvolveNet模型
输入：冰形(x,y,z) + 工况(H,V,T,LWC,MVD,AOA) + 时间差
输出：预测的冰形(x,y,z)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class PositionalEncoding(nn.Module):
    """位置编码模块"""
    def __init__(self, d_model, max_len=1000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]


class TransformerEncoderLayer(nn.Module):
    """Transformer编码器层"""
    def __init__(self, d_model, nhead, dim_feedforward=2048, dropout=0.1):
        super().__init__()
        self.self_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=True)
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward, d_model)
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
    
    def forward(self, src, src_mask=None):
        # Self-attention
        src2, _ = self.self_attn(src, src, src, attn_mask=src_mask)
        src = src + self.dropout1(src2)
        src = self.norm1(src)
        
        # Feed forward
        src2 = self.linear2(self.dropout(F.relu(self.linear1(src))))
        src = src + self.dropout2(src2)
        src = self.norm2(src)
        
        return src


class IceShapeEncoder(nn.Module):
    """冰形编码器"""
    def __init__(self, input_dim=10, d_model=256, nhead=8, num_layers=6, dropout=0.1):
        super().__init__()
        
        # 输入投影层
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model)
        
        # Transformer编码器层
        self.transformer_layers = nn.ModuleList([
            TransformerEncoderLayer(d_model, nhead, d_model * 4, dropout)
            for _ in range(num_layers)
        ])
        
        self.dropout = nn.Dropout(dropout)
        self.d_model = d_model
    
    def forward(self, x):
        """
        Args:
            x: [B, N, input_dim] 输入特征
        Returns:
            encoded: [B, N, d_model] 编码后的特征
        """
        # 输入投影
        x = self.input_projection(x) * math.sqrt(self.d_model)
        
        # 位置编码
        x = x.transpose(0, 1)  # [N, B, d_model]
        x = self.pos_encoder(x)
        x = x.transpose(0, 1)  # [B, N, d_model]
        
        x = self.dropout(x)
        
        # 通过Transformer层
        for layer in self.transformer_layers:
            x = layer(x)
        
        return x


class ConditionFusion(nn.Module):
    """工况融合模块 - 使用FiLM机制"""
    def __init__(self, d_model=256, condition_dim=7):  # 6个工况 + 1个时间差
        super().__init__()
        
        # 工况编码器
        self.condition_encoder = nn.Sequential(
            nn.Linear(condition_dim, d_model),
            nn.ReLU(),
            nn.Linear(d_model, d_model),
            nn.ReLU(),
            nn.Linear(d_model, d_model * 2)  # 输出gamma和beta
        )
        
    def forward(self, features, conditions):
        """
        Args:
            features: [B, N, d_model] 编码后的特征
            conditions: [B, condition_dim] 工况信息
        Returns:
            fused_features: [B, N, d_model] 融合后的特征
        """
        # 编码工况信息
        condition_params = self.condition_encoder(conditions)  # [B, d_model * 2]
        
        # 分离gamma和beta
        gamma, beta = torch.chunk(condition_params, 2, dim=-1)  # 各自 [B, d_model]
        
        # 扩展维度以匹配特征
        gamma = gamma.unsqueeze(1)  # [B, 1, d_model]
        beta = beta.unsqueeze(1)   # [B, 1, d_model]
        
        # FiLM调制
        fused_features = features * gamma + beta
        
        return fused_features


class IceShapeDecoder(nn.Module):
    """冰形解码器"""
    def __init__(self, d_model=256, output_dim=2, num_layers=3):  # 改为2维输出
        super().__init__()

        # 解码器层
        layers = []
        for i in range(num_layers - 1):
            layers.extend([
                nn.Linear(d_model, d_model),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])

        # 输出层
        layers.append(nn.Linear(d_model, output_dim))

        self.decoder = nn.Sequential(*layers)

    def forward(self, x):
        """
        Args:
            x: [B, N, d_model] 融合后的特征
        Returns:
            output: [B, N, output_dim] 预测的冰形坐标 (x, y)
        """
        return self.decoder(x)


class PhysEvolveNet(nn.Module):
    """
    新版PhysEvolveNet主模型
    输入：冰形 + 工况 + 时间 -> 输出：预测冰形
    """
    def __init__(self, input_dim=10, d_model=256, nhead=8, num_encoder_layers=6,
                 num_decoder_layers=3, output_dim=2, dropout=0.1):  # 改为2维输出
        super().__init__()

        # 编码器
        self.encoder = IceShapeEncoder(
            input_dim=input_dim,
            d_model=d_model,
            nhead=nhead,
            num_layers=num_encoder_layers,
            dropout=dropout
        )

        # 工况融合模块
        self.condition_fusion = ConditionFusion(
            d_model=d_model,
            condition_dim=7  # 6个工况参数 + 1个时间差
        )

        # 解码器
        self.decoder = IceShapeDecoder(
            d_model=d_model,
            output_dim=output_dim,
            num_layers=num_decoder_layers
        )
    
    def forward(self, input_features):
        """
        Args:
            input_features: [B, N, 10] 输入特征
                           前3维：冰形坐标(x,y,z)
                           中6维：工况参数(H,V,T,LWC,MVD,AOA)
                           后1维：时间差
        Returns:
            predicted_shape: [B, N, 2] 预测的冰形坐标 (x, y)
        """
        batch_size, num_points, _ = input_features.shape

        # 分离输入特征
        ice_coords_xy = input_features[:, :, :2]  # [B, N, 2] 只取x,y坐标用于残差连接
        conditions = input_features[:, 0, 3:]  # [B, 7] 工况+时间（每个点都相同，取第一个点即可）

        # 编码输入特征
        encoded_features = self.encoder(input_features)  # [B, N, d_model]

        # 工况融合
        fused_features = self.condition_fusion(encoded_features, conditions)  # [B, N, d_model]

        # 解码得到预测的冰形
        predicted_shape = self.decoder(fused_features)  # [B, N, 2]

        # 残差连接：预测位移 + 原始x,y坐标
        predicted_shape = predicted_shape + ice_coords_xy

        return predicted_shape


def create_model(input_dim=10, d_model=256, nhead=8, num_encoder_layers=6,
                num_decoder_layers=3, output_dim=2, dropout=0.1):  # 改为2维输出
    """创建模型实例"""
    model = PhysEvolveNet(
        input_dim=input_dim,
        d_model=d_model,
        nhead=nhead,
        num_encoder_layers=num_encoder_layers,
        num_decoder_layers=num_decoder_layers,
        output_dim=output_dim,
        dropout=dropout
    )
    return model


if __name__ == "__main__":
    # 测试模型
    batch_size = 4
    num_points = 200
    input_dim = 10
    
    # 创建模型
    model = create_model()
    
    # 创建测试输入
    test_input = torch.randn(batch_size, num_points, input_dim)
    
    # 前向传播
    with torch.no_grad():
        output = model(test_input)
    
    print(f"输入形状: {test_input.shape}")
    print(f"输出形状: {output.shape}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    print("模型测试通过！")
