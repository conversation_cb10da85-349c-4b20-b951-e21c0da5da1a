import torch
import torch.nn as nn


# ==============================================================================
# 模块一：几何编码器 (来自您的 PointTransformer.py)
# ==============================================================================
class TransformerEncoderLayer(nn.Module):
    def __init__(self, dim, num_heads=4):
        super().__init__()
        self.attn = nn.MultiheadAttention(embed_dim=dim, num_heads=num_heads, batch_first=True)
        self.ffn = nn.Sequential(nn.Linear(dim, dim * 4), nn.ReLU(), nn.Linear(dim * 4, dim))
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)

    def forward(self, x):
        attn_out, _ = self.attn(x, x, x)
        x = self.norm1(x + attn_out)
        x = self.norm2(x + self.ffn(x))
        return x


class GeometricEncoder(nn.Module):
    def __init__(self, input_dim=5, hidden_dim=64, num_heads=4, num_layers=2):
        super().__init__()
        self.input_mlp = nn.Sequential(nn.Linear(input_dim, hidden_dim), nn.ReLU(), nn.Linear(hidden_dim, hidden_dim))
        self.local_transformer = nn.Sequential(
            *[TransformerEncoderLayer(hidden_dim, num_heads) for _ in range(num_layers)])
        self.global_transformer = nn.Sequential(
            *[TransformerEncoderLayer(hidden_dim, num_heads) for _ in range(num_layers)])

    def forward(self, points):
        features = self.input_mlp(points)
        local_features = self.local_transformer(features)
        global_features = self.global_transformer(local_features)
        return global_features


# ==============================================================================
# 模块二：循环演化模块 (我们讨论的 GruAndFilm)
# ==============================================================================
class RecurrentEvolutionModule(nn.Module):
    def __init__(self, condition_dim=5, feature_dim=64, hidden_dim=256):
        super().__init__()
        self.film_generator = nn.Sequential(
            nn.Linear(condition_dim, 128),
            nn.ReLU(),
            nn.Linear(128, feature_dim * 2)
        )
        self.pooling = nn.AdaptiveAvgPool1d(1)
        self.gru_cell = nn.GRUCell(input_size=feature_dim, hidden_size=hidden_dim)

    def forward(self, F_geo_t, C_t, h_prev):
        film_params = self.film_generator(C_t)
        gamma, beta = torch.chunk(film_params, 2, dim=-1)
        gamma = gamma.unsqueeze(1)
        beta = beta.unsqueeze(1)
        F_mod_t = F_geo_t * gamma + beta
        g_mod_t = self.pooling(F_mod_t.transpose(1, 2)).squeeze(-1)
        h_next = self.gru_cell(g_mod_t, h_prev)
        return F_mod_t, h_next


# ==============================================================================
# 模块三：位移回归模块 (Decoder)
# ==============================================================================
class DisplacementDecoder(nn.Module):
    def __init__(self, input_dim, hidden_dim=128, output_dim=2):
        super().__init__()
        self.mlp_head = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )

    def forward(self, F_fused):
        return self.mlp_head(F_fused)


# ==============================================================================
# 最终组装模型：PhysEvolveNet
# ==============================================================================
class PhysEvolveNet(nn.Module):
    """
    完整的 PhysEvolveNet 模型
    - 它将所有模块组合起来，并处理时间序列的循环。
    """

    def __init__(self, geo_input_dim=5, cond_input_dim=5, feature_dim=64, hidden_dim=256):
        super().__init__()

        # 实例化所有子模块
        self.encoder = GeometricEncoder(input_dim=geo_input_dim, hidden_dim=feature_dim)
        self.recurrent_core = RecurrentEvolutionModule(
            condition_dim=cond_input_dim,
            feature_dim=feature_dim,
            hidden_dim=hidden_dim
        )
        self.decoder = DisplacementDecoder(input_dim=feature_dim + hidden_dim)

    def forward(self, P_sequence, C_sequence, h_initial):
        """
        处理整个时间序列的前向传播
        Args:
            P_sequence (torch.Tensor): 几何输入序列。[B, L, N, 5]
            C_sequence (torch.Tensor): 工况输入序列。[B, L, 5]
            h_initial (torch.Tensor):  初始的历史记忆。[B, D_h]

        Returns:
            torch.Tensor: 预测的位移场序列。[B, L, N, 2]
        """
        batch_size, seq_len, _, _ = P_sequence.shape

        # 用于存储每一步的预测结果
        all_displacements = []

        # 初始化历史记忆
        h_prev = h_initial

        # 沿时间维度进行循环
        for t in range(seq_len):
            # 1. 获取当前时间步的数据
            P_t = P_sequence[:, t, :, :]  # [B, N, 5]
            C_t = C_sequence[:, t, :]  # [B, 5]

            # 2. 几何编码
            F_geo_t = self.encoder(P_t)

            # 3. 循环演化，得到融合特征和新记忆
            F_mod_t, h_next = self.recurrent_core(F_geo_t, C_t, h_prev)

            # 4. 解码，得到位移预测
            # 将新记忆广播并与逐点特征拼接
            h_next_expanded = h_next.unsqueeze(1).expand(-1, P_t.shape[1], -1)
            F_fused = torch.cat([F_mod_t, h_next_expanded], dim=-1)
            delta_P_t = self.decoder(F_fused)

            # 5. 保存当前步的预测结果
            all_displacements.append(delta_P_t)

            # 6. 更新历史记忆，为下一步做准备
            h_prev = h_next

        # 将所有时间步的结果拼接成一个张量
        # [B, L, N, 2]
        return torch.stack(all_displacements, dim=1)


# ==============================================================================
# 测试代码 (验证最终的 PhysEvolveNet 模型)
# ==============================================================================
if __name__ == "__main__":
    print(f"\n--- 正在测试完整的 PhysEvolveNet 模型 ---")

    # 假设参数
    batch_size = 32
    seq_len = 8  # 输入序列长度 (来自我们的Dataset)
    num_points = 262
    geo_input_dim = 5
    cond_input_dim = 5
    hidden_dim = 256

    # 创建一个随机的输入序列来模拟我们的数据
    dummy_p_seq = torch.randn(batch_size, seq_len, num_points, geo_input_dim)
    dummy_c_seq = torch.randn(batch_size, seq_len, cond_input_dim)
    dummy_h_initial = torch.zeros(batch_size, hidden_dim)  # 初始记忆通常为0

    # 初始化完整的模型
    model = PhysEvolveNet(
        geo_input_dim=geo_input_dim,
        cond_input_dim=cond_input_dim,
        hidden_dim=hidden_dim
    )

    # 将输入序列送入模型
    output_displacement_seq = model(dummy_p_seq, dummy_c_seq, dummy_h_initial)

    # 打印形状以进行验证
    print(f"输入几何序列形状: {dummy_p_seq.shape}")
    print(f"输入工况序列形状: {dummy_c_seq.shape}")
    print(f"输入初始记忆形状: {dummy_h_initial.shape}")
    print("-" * 20)
    print(f"最终模型输出的位移场序列形状: {output_displacement_seq.shape}")

    # 检查输出形状是否正确
    expected_shape = (batch_size, seq_len, num_points, 2)
    assert output_displacement_seq.shape == expected_shape, "最终输出形状错误!"

    print("\nPhysEvolveNet 模型组装完成，运行正常，输入输出形状匹配！")