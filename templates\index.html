<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平行实验实时展示界面</title>
    <!-- 禁用favicon请求，避免404错误 -->
    <link rel="icon" href="data:,">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // 全局变量来跟踪加载状态
        let stlLoaderReady = false;
        let orbitControlsReady = false;
        
        // STL模型动画联动变量
        let stlMesh = null;
        let stlControls = null;
        let stlScene = null;
        let stlRenderer = null;
        let stlCamera = null;
        let stlAnimationSpeed = 1.0; // 基础旋转速度
        let stlTurbulenceEffect = 0; // 湍流效果强度
        let stlIcingEffect = 0; // 结冰效果程度
        
        // 冰点效果相关变量
        let iceParticleSystem = null; // 冰点粒子系统
        let iceParticles = []; // 冰点粒子数组
        let frostTexture = null; // 霜冻纹理
        
        // 增强动画效果相关变量
        let airflowParticleSystem = null; // 气流粒子系统
        let environmentLights = {}; // 环境光照系统
        let stlPitchAngle = 0; // 俯仰角
        let stlYawAngle = 0; // 偏航角
        let stlRollAngle = 0; // 翻滚角
        let skyBox = null; // 天空盒
        let engineSound = null; // 引擎声音
        let weatherEffects = null; // 天气效果
        
        // 加载 OrbitControls
        function loadOrbitControls() {
            const script = document.createElement('script');
            script.src = 'https://threejs.org/examples/js/controls/OrbitControls.js';
            script.onload = function() {
                console.log('OrbitControls已加载');
                orbitControlsReady = true;
            };
            script.onerror = function() {
                console.warn('OrbitControls加载失败，尝试备用CDN...');
                const script2 = document.createElement('script');
                script2.src = 'https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js';
                script2.onload = function() {
                    console.log('OrbitControls从备用CDN加载成功');
                    orbitControlsReady = true;
                };
                script2.onerror = function() {
                    console.error('所有OrbitControls CDN都加载失败');
                    orbitControlsReady = false;
                };
                document.head.appendChild(script2);
            };
            document.head.appendChild(script);
        }
        
        // 使用多个备用CDN加载STLLoader
        function loadSTLLoader() {
            const cdnUrls = [
                'https://threejs.org/examples/js/loaders/STLLoader.js',
                'https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/STLLoader.js',
                'https://unpkg.com/three@0.128.0/examples/js/loaders/STLLoader.js'
            ];
            
            function tryLoadScript(index) {
                if (index >= cdnUrls.length) {
                    console.error('所有STLLoader CDN都加载失败');
                    stlLoaderReady = false;
                    return;
                }
                
                const script = document.createElement('script');
                script.src = cdnUrls[index];
                script.onload = function() {
                    console.log(`STLLoader已从CDN ${index + 1}成功加载`);
                    stlLoaderReady = true;
                    // 检查是否存在THREE.STLLoader
                    if (typeof THREE !== 'undefined' && typeof THREE.STLLoader !== 'undefined') {
                        console.log('THREE.STLLoader确认可用');
                    } else {
                        console.warn('THREE.STLLoader仍不可用，可能需要等待');
                    }
                };
                script.onerror = function() {
                    console.warn(`STLLoader CDN ${index + 1} 加载失败，尝试下一个...`);
                    tryLoadScript(index + 1);
                };
                document.head.appendChild(script);
            }
            
            tryLoadScript(0);
        }
        
        // 当DOM加载完成后加载所有必要的库
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                loadOrbitControls();
                loadSTLLoader();
            });
        } else {
            loadOrbitControls();
            loadSTLLoader();
        }
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        .header {
            padding: 25px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            color: white;
        }

        .header-main {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .header-left {
            flex: 1;
        }

        .header-left h1 {
            margin: 0;
            font-size: 36px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            font-family: "'Segoe UI', 'Microsoft YaHei', sans-serif";
            line-height: 1.2;
        }

        .header-right {
            flex: 1;
            text-align: right;
        }

        .header-subtitle {
            margin: 0;
            font-size: 18px;
            opacity: 0.9;
            font-weight: 500;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            line-height: 1.3;
        }

        .header-status-bar {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .status-text {
            font-size: 13px;
            font-weight: 500;
        }

        .header-info {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .info-card {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.15);
            padding: 12px 16px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            min-width: 140px;
        }

        .info-card:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .info-icon {
            font-size: 18px;
            margin-right: 10px;
        }

        .info-content {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 11px;
            opacity: 0.8;
            font-weight: 400;
            margin-bottom: 2px;
        }

        .info-value {
            font-size: 13px;
            font-weight: 600;
            color: #ffd700;
        }
        .input-section {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: space-between;
            padding: 25px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            gap: 15px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .input-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
        }
        .input-group {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
            min-width: 150px;
        }
        .input-group label {
            font-weight: 600;
            color: #4a5568;
            font-size: 14px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        .input-group input {
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            width: 100%;
            font-size: 14px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .input-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }
        .input-group input::placeholder {
            color: #a0aec0;
            font-style: italic;
        }

        .input-section button {
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            min-width: 140px;
            position: relative;
            overflow: hidden;
        }
        .input-section button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        .input-section button:hover::before {
            left: 100%;
        }
        .input-section button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
        .input-section button:active {
            transform: translateY(0);
        }
        .container {
            display: flex;
            flex: 1;
        }
        .left-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            gap: 25px;
            background-color: transparent;
        }
        .right-column {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 25px;
            padding: 20px;
            background-color: transparent;
        }
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            padding: 20px;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-height: 400px; /* 设置最小高度 */
            height: auto; /* 允许自适应高度 */
        }
        .chart-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(31, 38, 135, 0.5);
        }

        .chart-container canvas {
            width: 100% !important;
            height: 100% !important;
            max-width: 100%;
            max-height: none !important; /* 移除最大高度限制 */
        }
        .chart-container h3 {
            margin-bottom: 18px;
            margin-top: 0; /* 移除顶部间距 */
            font-size: 18px;
            color: #1f2937;
            font-weight: 700;
            text-align: center;
            position: relative;
            z-index: 1;
            flex-shrink: 0; /* 防止标题被压缩 */
            padding: 0 12px;
            line-height: 1.4;
            letter-spacing: 0.5px;
        }

        /* 右栏图表标题特殊样式 */
        .right-column .chart-container h3 {
            font-size: 16px;
            font-weight: 700;
            background: linear-gradient(135deg, #1f2937 0%, #4b5563 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: none;
            position: relative;
            padding-bottom: 8px;
        }

        .right-column .chart-container h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #9ca3af, transparent);
            border-radius: 1px;
        }

        /* 升力系数标题 */
        .right-column .chart-container:nth-child(1) h3::after {
            background: linear-gradient(90deg, transparent, #3b82f6, transparent);
        }

        /* 阻力系数标题 */
        .right-column .chart-container:nth-child(2) h3::after {
            background: linear-gradient(90deg, transparent, #10b981, transparent);
        }

        /* 2D冰形容器样式优化 */
        .ice-2d-container {
            width: 100%;
            background: transparent;
            position: relative;
            margin-bottom: 15px;
            border-radius: 0;
            border: none;
            box-shadow: none;
            overflow: hidden;
            transition: none;

            /* 响应式高度设置 */
            height: 320px; /* 默认高度 */
            min-height: 280px; /* 最小高度 */
            max-height: 500px; /* 最大高度 */
        }

        /* 根据屏幕宽度调整2D容器高度 */
        @media (min-width: 1200px) {
            .ice-2d-container {
                height: 380px; /* 大屏幕时增加高度 */
            }
        }

        @media (min-width: 1400px) {
            .ice-2d-container {
                height: 420px; /* 超大屏幕时进一步增加高度 */
            }
        }

        @media (min-width: 1600px) {
            .ice-2d-container {
                height: 460px; /* 超宽屏时的最佳高度 */
            }
        }

        /* 根据屏幕高度调整2D容器高度 */
        @media (min-height: 800px) {
            .ice-2d-container {
                height: max(380px, 35vh); /* 高屏幕时使用视口高度的35% */
            }
        }

        @media (min-height: 1000px) {
            .ice-2d-container {
                height: max(420px, 38vh); /* 更高屏幕时使用视口高度的38% */
            }
        }



        /* 2D冰形占位符样式 */
        .ice-2d-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #6b7280;
            font-family: "'Segoe UI', 'Microsoft YaHei', sans-serif";
            z-index: 10;
        }

        .placeholder-icon {
            font-size: 32px;
            margin-bottom: 12px;
            opacity: 0.8;
        }

        .placeholder-text {
            font-size: 15px;
            font-weight: 600;
            color: #4b5563;
            margin-bottom: 6px;
            line-height: 1.4;
        }

        .placeholder-subtitle {
            font-size: 12px;
            color: #9ca3af;
            font-weight: 400;
            opacity: 0.8;
        }

        /* 2D冰形图表Canvas样式 */
        .ice-2d-container canvas {
            width: 100% !important;
            height: 100% !important;
            border-radius: 0;
            background: transparent;
        }

        /* 确保2D图表在容器中居中且保持比例 */
        .ice-2d-container .chart-wrapper {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
        }
        
        /* 图表内容区域样式 - 只针对右栏的Chart.js图表 */
        .right-column .chart-content {
            flex: 1; /* 占用剩余空间 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 280px; /* 增加最小高度 */
            max-height: 520px; /* 增加最大高度限制 */
            position: relative;
            overflow: hidden; /* 防止图表溢出 */
            padding: 8px; /* 添加内边距 */
        }

        /* 确保canvas元素有正确的尺寸 */
        .right-column .chart-content canvas {
            min-width: 100% !important;
            width: 100% !important;
            height: auto !important;
        }

        /* 确保2D内容容器有正确的尺寸 */
        #water2DContent, #heat2DContent {
            width: 100% !important;
            min-width: 100% !important;
            display: flex !important;
            flex-direction: column !important;
        }

        /* 自动切换按钮动画效果 */
        .chart-btn {
            transition: all 0.3s ease;
        }

        .chart-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 自动切换按钮激活状态动画 */
        #waterAutoSwitchBtn.active, #heatAutoSwitchBtn.active {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3); }
            50% { box-shadow: 0 4px 16px rgba(16, 185, 129, 0.6); }
            100% { box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3); }
        }

        /* 2D/3D切换动画 */
        .view-transition {
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
        }

        .view-fade-out {
            opacity: 0 !important;
            transform: scale(0.95) translateY(8px) !important;
            filter: blur(1px) !important;
        }

        .view-fade-in {
            opacity: 1 !important;
            transform: scale(1) translateY(0) !important;
            filter: blur(0) !important;
        }

        /* 确保初始状态 */
        .chart-content canvas,
        .chart-content > div {
            opacity: 1;
            transform: scale(1) translateY(0);
            filter: blur(0);
        }

        /* 3D容器动画 */
        #water3DContainer, #heat3DContainer {
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transform-origin: center center;
        }

        /* 2D内容动画 */
        #water2DContent, #heat2DContent {
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transform-origin: center center;
        }

        /* 图表容器动画 */
        .chart-content {
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* 切换时的加载效果 */
        .switching-view {
            position: relative;
        }

        .switching-view::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 1.5s ease-in-out;
            pointer-events: none;
            z-index: 10;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 右栏图表容器特殊样式 */
        .right-column .chart-container {
            background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
            border: 1px solid rgba(203, 213, 225, 0.6);
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.06),
                0 1px 3px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .right-column .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(59, 130, 246, 0.3) 25%,
                rgba(16, 185, 129, 0.3) 75%,
                transparent 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .right-column .chart-container:hover {
            transform: translateY(-4px) scale(1.01);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.12),
                0 4px 16px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.95);
            border-color: rgba(59, 130, 246, 0.2);
        }

        .right-column .chart-container:hover::before {
            opacity: 1;
        }

        /* 升力系数图表特殊样式 */
        .right-column .chart-container:nth-child(1) {
            border-left: 3px solid rgba(59, 130, 246, 0.4);
        }

        .right-column .chart-container:nth-child(1):hover {
            border-left-color: rgba(59, 130, 246, 0.8);
        }

        /* 阻力系数图表特殊样式 */
        .right-column .chart-container:nth-child(2) {
            border-left: 3px solid rgba(16, 185, 129, 0.4);
        }

        .right-column .chart-container:nth-child(2):hover {
            border-left-color: rgba(16, 185, 129, 0.8);
        }

        /* 图表头部样式 */
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 0 8px;
        }

        .chart-toolbar {
            display: flex;
            gap: 6px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .chart-container:hover .chart-toolbar {
            opacity: 1;
        }

        .chart-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 6px;
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .chart-btn:hover {
            background: rgba(59, 130, 246, 0.2);
            transform: scale(1.1);
        }

        .chart-btn:active {
            transform: scale(0.95);
        }

        /* 操作按钮区域样式 */
        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: "'Segoe UI', 'Microsoft YaHei', sans-serif";
            min-width: 120px;
            justify-content: center;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .action-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        .action-btn.secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #4b5563;
            border: 1px solid rgba(203, 213, 225, 0.8);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .action-btn.secondary:hover {
            transform: translateY(-2px);
            background: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .btn-icon {
            font-size: 16px;
        }

        .btn-text {
            font-size: 14px;
        }

        /* 误差分析面板样式 */
        .error-metrics {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .error-metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 8px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 6px;
            border: 1px solid rgba(229, 231, 235, 0.5);
        }

        .error-label {
            font-size: 10px;
            color: #6b7280;
            font-weight: 500;
            min-width: 60px;
        }

        .error-value {
            font-size: 11px;
            font-weight: bold;
            color: #1f2937;
            text-align: right;
        }

        .error-value.good {
            color: #059669;
        }

        .error-value.warning {
            color: #d97706;
        }

        .error-value.error {
            color: #dc2626;
        }
        
        /* 左栏容器保持原有布局 */
        .left-column .chart-container {
            align-items: center;
            justify-content: flex-start; /* 从顶部开始布局 */
        }
        
        .chart-container button {
            margin-top: 15px;
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .chart-container button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 25px;
            background: transparent;
            border: none;
            box-shadow: none;
        }
        .footer button {
            padding: 12px 25px;
            color: #fff;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .footer .logout {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .footer .logout:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.5);
        }
        .footer .configure {
            background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
        }
        .footer .configure:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 52, 212, 0.5);
        }
        .stl-container {
            width: 100%;
            height: 320px; /* 调整高度更小，更紧凑 */
            margin: 10px auto;
            border: none; /* 移除边框 */
            border-radius: 8px;
            box-shadow: none; /* 移除阴影 */
            background: #ffffff; /* 纯白色背景与Three.js场景匹配 */
            position: relative; /* 为提示信息定位 */
            overflow: hidden;
        }
        
        /* STL加载状态样式 */
        .stl-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #ffffff;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: opacity 0.5s ease-out;
            border: none;
        }
        
        .stl-loading.fade-out {
            opacity: 0;
            pointer-events: none;
        }
        
        /* 3D冰形加载状态样式 */
        .ice3d-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #ffffff;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: opacity 0.5s ease-out;
            border: none;
        }
        
        .ice3d-loading.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        /* HTC和Beta 3D加载状态样式 */
        .htc-beta-3d-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: opacity 0.5s ease-out;
            border: none;
            border-radius: 8px;
        }

        .htc-beta-3d-loading.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        /* HTC和Beta 3D控制面板样式 */
        .htc-beta-3d-controls {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }

        .htc-beta-3d-controls div:first-child {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }

        .htc-beta-3d-controls div:not(:first-child) {
            color: #666;
            margin: 2px 0;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f0f0f0;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: #666666;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
        }
        
        .loading-progress {
            width: 200px;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            margin-top: 10px;
            overflow: hidden;
        }
        
        .loading-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        /* 气动系数图表加载状态样式 */
        .aero-chart-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: opacity 0.5s ease-out;
            border: none;
        }
        
        .aero-chart-loading.fade-out {
            opacity: 0;
            pointer-events: none;
        }
        
        .aero-loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f0f0f0;
            border-top: 3px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 12px;
        }
        
        .aero-loading-text {
            color: #4a5568;
            font-size: 13px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 8px;
        }
        
        .aero-loading-subtitle {
            color: #718096;
            font-size: 11px;
            text-align: center;
        }
        .airfoil-info {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(255,255,255,0.9);
            padding: 12px;
            border-radius: 10px;
            font-size: 12px;
            z-index: 50;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            pointer-events: none; /* 允许鼠标事件穿透到3D场景 */
        }
        .airfoil-info .param {
            margin-bottom: 6px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .airfoil-info .param-name {
            font-weight: bold;
            color: #333;
            font-size: 12px;
        }
        .airfoil-info .param-value {
            color: #007bff;
            font-weight: bold;
            font-size: 12px;
        }
        
        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .chart-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        .chart-container:nth-child(1) { animation-delay: 0.1s; }
        .chart-container:nth-child(2) { animation-delay: 0.2s; }
        .chart-container:nth-child(3) { animation-delay: 0.3s; }
        .chart-container:nth-child(4) { animation-delay: 0.4s; }
        
        /* 响应式设计 */
        @media (max-width: 1024px) {
            .header-main {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-left,
            .header-right {
                flex: none;
            }

            .header-right {
                text-align: center;
            }

            .header-info {
                justify-content: center;
                gap: 10px;
            }

            .info-card {
                min-width: 120px;
                font-size: 12px;
            }
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .right-column {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(4, 1fr);
            }

            .input-section {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .input-group {
                min-width: auto;
            }

            .chart-toolbar {
                opacity: 1; /* 在移动设备上始终显示 */
            }

            .chart-btn {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 8px;
            }

            .action-btn {
                min-width: auto;
                width: 100%;
            }

            .header-info {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .info-card {
                min-width: auto;
                padding: 8px 12px;
            }

            .footer {
                margin-top: 20px;
                padding: 15px;
            }

            .footer button {
                margin: 0 5px;
                padding: 10px 20px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .header-left h1 {
                font-size: 24px;
            }

            .header-subtitle {
                font-size: 14px;
            }

            .info-card {
                padding: 6px 8px;
            }

            .info-icon {
                font-size: 14px;
            }

            .info-label {
                font-size: 10px;
            }

            .info-value {
                font-size: 11px;
            }

            .footer {
                flex-direction: column;
                gap: 10px;
            }

            .footer button {
                width: 100%;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="content">
        <div class="header">
            <div class="header-main">
                <div class="header-left">
                    <h1>平行实验实时展示界面</h1>
                </div>
                <div class="header-right">
                    <p class="header-subtitle">基于深度学习的翼型结冰预测与气动分析系统</p>
                </div>
            </div>
            <div class="header-status-bar">
                <div class="status-indicator">
                    <span class="status-dot"></span>
                    <span class="status-text">系统运行正常</span>
                </div>
            </div>
            <div class="header-info">
                <div class="info-card">
                    <span class="info-icon">🧪</span>
                    <div class="info-content">
                        <span class="info-label">当前实验翼型</span>
                        <span class="info-value">NACA0012</span>
                    </div>
                </div>
                <div class="info-card">
                    <span class="info-icon">🤖</span>
                    <div class="info-content">
                        <span class="info-label">AI模型版本</span>
                        <span class="info-value">v2.1.0</span>
                    </div>
                </div>
                <div class="info-card">
                    <span class="info-icon">⏰</span>
                    <div class="info-content">
                        <span class="info-label">当前时间</span>
                        <span class="info-value" id="current-time">加载中...</span>
                    </div>
                </div>
                <div class="info-card">
                    <span class="info-icon">📊</span>
                    <div class="info-content">
                        <span class="info-label">预测次数</span>
                        <span class="info-value" id="prediction-count">0</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="input-section">
            <div class="input-group">
                <label for="velocity">🚀 速度 (m/s):</label>
                <input type="number" id="velocity" placeholder="输入飞行速度" step="0.1">
            </div>
            <div class="input-group">
                <label for="temperature">🌡️ 温度 (K):</label>
                <input type="number" id="temperature" placeholder="输入环境温度" step="0.1">
            </div>
            <div class="input-group">
                <label for="lwc">💧 液态水含量 (g/m³):</label>
                <input type="number" id="lwc" placeholder="输入液态水含量" step="0.01">
            </div>
            <div class="input-group">
                <label for="mvd">🔬 水滴直径 (μm):</label>
                <input type="number" id="mvd" placeholder="输入水滴直径" step="0.1">
            </div>
            <div class="input-group">
                <label for="height">📏 高度 (m):</label>
                <input type="number" id="height" placeholder="输入飞行高度" step="1" value="0">
            </div>
            <div class="input-group">
                <label for="aoa">📐 攻角 (°):</label>
                <input type="number" id="aoa" placeholder="输入攻角" step="0.1" value="0">
            </div>
            <button onclick="predictIceShape()">
                <span style="margin-right: 8px;">🔮</span>AI智能预测
            </button>
        </div>

        <div class="container">
            <!-- 左栏 -->
            <div class="left-column">
                <div class="chart-container">
                    <h3>🛩️ 工况参数实时联动飞机模型 - NACA0012翼型</h3>
                    <div class="stl-container" id="stlContainer">
                        <!-- 加载状态指示器 -->
                        <div class="stl-loading" id="stlLoading">
                            <div class="loading-spinner"></div>
                            <div class="loading-text" id="loadingText">正在加载3D模型...</div>
                            <div class="loading-progress">
                                <div class="loading-progress-bar" id="loadingProgressBar"></div>
                            </div>
                        </div>
                        
                        <!-- 参数面板 -->
                        <div class="airfoil-info">
                            <div style="margin-bottom: 8px; font-weight: bold; color: #333;">🛩️ NACA0012翼型参数</div>
                            <div class="param">
                                <span class="param-name">弦长:</span>
                                <span class="param-value">1.0 m</span>
                            </div>
                            <div class="param">
                                <span class="param-name">最大厚度:</span>
                                <span class="param-value">12% c</span>
                            </div>
                            <div class="param">
                                <span class="param-name">厚度位置:</span>
                                <span class="param-value">30% c</span>
                            </div>
                            <div class="param">
                                <span class="param-name">弯度:</span>
                                <span class="param-value">0% (对称)</span>
                            </div>
                            <div class="param">
                                <span class="param-name">前缘半径:</span>
                                <span class="param-value">1.58% c</span>
                            </div>
                        </div>
                        
                        <!-- 动画状态指示器 -->
                        <div id="stlAnimationStatus" style="position: absolute; bottom: 15px; right: 15px; background: rgba(255,255,255,0.9); padding: 12px; border-radius: 10px; font-size: 12px; z-index: 60; display: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1); pointer-events: none; min-width: 180px; font-family: 'Segoe UI', Arial, sans-serif;">
                            <div style="margin-bottom: 8px; font-weight: bold; color: #333; font-size: 13px; border-bottom: 1px solid rgba(0,0,0,0.1); padding-bottom: 4px;">🎬 动画联动状态</div>
                            <div class="param" style="margin-bottom: 6px; display: flex; justify-content: space-between; align-items: center;">
                                <span class="param-name" style="font-weight: bold; color: #333; font-size: 12px;">⚡ 速度:</span>
                                <span id="animationSpeed" class="param-value" style="color: #007bff; font-weight: bold; font-size: 12px; transition: all 0.3s ease;">1.0x</span>
                            </div>
                            <div class="param" style="margin-bottom: 6px; display: flex; justify-content: space-between; align-items: center;">
                                <span class="param-name" style="font-weight: bold; color: #333; font-size: 12px;">🌡️ 温度:</span>
                                <span id="animationTemp" class="param-value" style="color: #28a745; font-weight: bold; font-size: 12px; transition: all 0.3s ease;">标准</span>
                            </div>
                            <div class="param" style="margin-bottom: 6px; display: flex; justify-content: space-between; align-items: center;">
                                <span class="param-name" style="font-weight: bold; color: #333; font-size: 12px;">💧 湍流:</span>
                                <span id="animationTurbulence" class="param-value" style="color: #6c757d; font-weight: bold; font-size: 12px; transition: all 0.3s ease;">无</span>
                            </div>
                            <div class="param" style="margin-bottom: 6px; display: flex; justify-content: space-between; align-items: center;">
                                <span class="param-name" style="font-weight: bold; color: #333; font-size: 12px;">🧊 结冰:</span>
                                <span id="animationIcing" class="param-value" style="color: #6c757d; font-weight: bold; font-size: 12px; transition: all 0.3s ease;">无</span>
                            </div>
                            <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid rgba(0,0,0,0.1); font-size: 10px; color: #666; text-align: center;">
                                <div>❄️ 冰点效果: 粒子飘落 + 霜冻材质</div>
                                <div>💨 气流可视化: 速度>10m/s时显示</div>
                                <div>💡 动态光照: 温度色温 + 强度调节</div>
                                <div>✈️ 飞行姿态: 俯仰/偏航/翻滚控制</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="chart-container">
                    <h3>🔬 AI智能结冰预测 - 2D/3D翼型冰形测绘图</h3>
                    <div id="ice3DContainer" style="width: 100%; height: 300px; position: relative; background: #ffffff; overflow: hidden; margin-bottom: 15px;">
                        <!-- 3D渲染容器 -->
                        <div id="ice3DCanvas" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0;"></div>
                        
                        <!-- 3D冰形加载状态指示器 -->
                        <div class="ice3d-loading" id="ice3DLoading">
                            <div class="loading-spinner"></div>
                            <div class="loading-text" id="ice3DLoadingText">正在初始化3D冰形场景...</div>
                            <div class="loading-progress">
                                <div class="loading-progress-bar" id="ice3DProgressBar"></div>
                            </div>
                        </div>
                        
                        <!-- 3D图例 -->
                        <div id="ice3DLegend" style="position: absolute; bottom: 15px; left: 15px; background: rgba(255,255,255,0.9); padding: 12px; border-radius: 10px; font-size: 12px; z-index: 50; display: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <div style="margin-bottom: 8px; font-weight: bold; color: #333;">📊 3D体积冰形图例</div>
                            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(31, 78, 121, 0.4), rgba(31, 78, 121, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                                <span>🔵 深蓝色 - 干净机翼(5层)</span>
                            </div>
                            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(214, 137, 16, 0.4), rgba(214, 137, 16, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                                <span>🟡 深橙色 - 实验冰形(5层)</span>
                            </div>
                            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(192, 57, 43, 0.4), rgba(192, 57, 43, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                                <span>🔴 深红色 - AI预测冰形(5层)</span>
                            </div>
                            <div style="display: flex; align-items: center; font-size: 11px; color: #666; margin-top: 8px; margin-bottom: 8px;">
                                <span>📚 每种形状由Z轴堆叠的多层面组成体积</span>
                            </div>
                            
                            <!-- 上下移动控制（集成在图例中） -->
                            <div style="border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;">
                                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 4px;">
                                    <span style="color: #007bff; font-weight: bold; font-size: 11px;">🔄 视角上下移动:</span>
                                    <button id="ice3DMoveToggle" onclick="toggleAutoMove()" style="background: #007bff; color: white; border: none; padding: 3px 6px; border-radius: 3px; font-size: 9px; cursor: pointer;">暂停</button>
                                </div>
                                <div style="color: #666; font-size: 10px;">点击交互会暂停自动移动</div>
                            </div>
                        </div>

                        <!-- 误差分析面板 -->
                        <div id="errorAnalysisPanel" style="position: absolute; bottom: 15px; right: 15px; background: rgba(255,255,255,0.95); padding: 12px; border-radius: 10px; font-size: 11px; z-index: 50; display: none; box-shadow: 0 4px 15px rgba(0,0,0,0.15); border: 1px solid rgba(203, 213, 225, 0.6);">
                            <div style="margin-bottom: 8px; font-weight: bold; color: #333; text-align: center;">📊 预测误差分析</div>
                            <div class="error-metrics">
                                <div class="error-metric-row">
                                    <span class="error-label">MAE:</span>
                                    <span class="error-value" id="maeValue">-</span>
                                </div>
                                <div class="error-metric-row">
                                    <span class="error-label">RMSE:</span>
                                    <span class="error-value" id="rmseValue">-</span>
                                </div>
                                <div class="error-metric-row">
                                    <span class="error-label">最大误差:</span>
                                    <span class="error-value" id="maxErrorValue">-</span>
                                </div>
                                <div class="error-metric-row">
                                    <span class="error-label">R²:</span>
                                    <span class="error-value" id="r2Value">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 2D部分 -->
                    <div id="ice2DContainer" class="ice-2d-container">
                        <!-- 2D图表容器，由JavaScript动态创建 -->
                        <div class="ice-2d-placeholder">
                            <div class="placeholder-icon">📊</div>
                            <div class="placeholder-text">预测完成后将显示2D冰形轮廓对比图</div>
                            <div class="placeholder-subtitle">AI智能分析翼型结冰形状变化</div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="action-btn primary" onclick="location.href='/details/icing_shape'">
                            <span class="btn-icon">📋</span>
                            <span class="btn-text">查看详细结果</span>
                        </button>
                        <button class="action-btn secondary" onclick="exportAllData()">
                            <span class="btn-icon">💾</span>
                            <span class="btn-text">导出数据</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右栏 -->
            <div class="right-column">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>升力系数随攻角变化曲线</h3>
                        <div class="chart-toolbar">
                            <button class="chart-btn" onclick="exportChartData('lift')" title="导出数据">💾</button>
                        </div>
                    </div>
                    <div class="chart-content">
                        <canvas id="chartLiftCoefficient"></canvas>
                        <!-- 升力系数加载动画 -->
                        <div id="liftChartLoading" class="aero-chart-loading">
                            <div class="aero-loading-spinner"></div>
                            <div class="aero-loading-text">正在生成升力系数曲线</div>
                            <div class="aero-loading-subtitle">AI智能分析中...</div>
                        </div>
                    </div>
                </div>
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>阻力系数随攻角变化曲线</h3>
                        <div class="chart-toolbar">
                            <button class="chart-btn" onclick="exportChartData('drag')" title="导出数据">💾</button>
                        </div>
                    </div>
                    <div class="chart-content">
                        <canvas id="chartDragCoefficient"></canvas>
                        <!-- 阻力系数加载动画 -->
                        <div id="dragChartLoading" class="aero-chart-loading">
                            <div class="aero-loading-spinner"></div>
                            <div class="aero-loading-text">正在生成阻力系数曲线</div>
                            <div class="aero-loading-subtitle">AI智能分析中...</div>
                        </div>
                    </div>
                </div>
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>💧 水滴收集系数 - 2D/3D分布图</h3>
                        <div class="chart-toolbar">
                            <button class="chart-btn" onclick="exportChartData('water')" title="导出数据">💾</button>
                            <button class="chart-btn" onclick="toggle3DView('water')" title="切换3D视图">🔄</button>
                            <button class="chart-btn" id="waterAutoSwitchBtn" onclick="toggleAutoSwitch('water')" title="自动2D/3D切换动画">🎬</button>
                        </div>
                    </div>
                    <div class="chart-content" style="display: flex; flex-direction: column; height: 100%; position: relative;">
                        <!-- 2D图表内容 -->
                        <div id="water2DContent" style="display: flex; flex-direction: column; height: 100%;">
                            <!-- 上部分：散点图 -->
                            <div style="flex: 2; position: relative; min-height: 60%;">
                                <canvas id="chartWaterCollection"></canvas>
                                <!-- 水滴收集系数加载动画 -->
                                <div id="waterChartLoading" class="aero-chart-loading">
                                    <div class="aero-loading-spinner"></div>
                                    <div class="aero-loading-text">正在生成水滴收集系数分布</div>
                                    <div class="aero-loading-subtitle">AI智能分析中...</div>
                                </div>
                            </div>
                            <!-- 下部分：坐标分布图 -->
                            <div style="flex: 1; position: relative; min-height: 35%; border-top: 1px solid #e5e7eb; padding-top: 8px;">
                                <canvas id="chartWaterChordwise"></canvas>
                                <div style="position: absolute; top: 2px; left: 10px; font-size: 11px; color: #666; font-weight: bold;">
                                    📊 坐标方向分布
                                </div>

                            </div>
                        </div>
                        <!-- 3D水滴收集系数容器 -->
                        <div id="water3DContainer" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; background: #ffffff; overflow: hidden; display: none; border-radius: 8px; z-index: 10;">
                            <div id="water3DCanvas" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0;"></div>
                            <!-- 3D加载状态指示器 -->
                            <div class="htc-beta-3d-loading" id="water3DLoading" style="display: none;">
                                <div class="loading-spinner"></div>
                                <div class="loading-text" id="water3DLoadingText">正在生成3D水滴收集系数分布...</div>
                                <div class="loading-progress">
                                    <div class="loading-progress-bar" id="water3DProgressBar"></div>
                                </div>
                            </div>
                            <!-- 3D控制面板 -->
                            <div class="htc-beta-3d-controls" style="position: absolute; top: 10px; right: 10px; background: rgba(255,255,255,0.9); padding: 10px; border-radius: 5px; font-size: 12px;">
                                <div>🎮 鼠标控制：</div>
                                <div>• 左键拖拽：旋转</div>
                                <div>• 滚轮：缩放</div>
                                <div>• 右键拖拽：平移</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>🔥 对流换热系数 - 2D/3D分布图</h3>
                        <div class="chart-toolbar">
                            <button class="chart-btn" onclick="exportChartData('heat')" title="导出数据">💾</button>
                            <button class="chart-btn" onclick="toggle3DView('heat')" title="切换3D视图">🔄</button>
                            <button class="chart-btn" id="heatAutoSwitchBtn" onclick="toggleAutoSwitch('heat')" title="自动2D/3D切换动画">🎬</button>
                        </div>
                    </div>
                    <div class="chart-content" style="display: flex; flex-direction: column; height: 100%; position: relative;">
                        <!-- 2D图表内容 -->
                        <div id="heat2DContent" style="display: flex; flex-direction: column; height: 100%;">
                            <!-- 上部分：散点图 -->
                            <div style="flex: 2; position: relative; min-height: 60%;">
                                <canvas id="chartHeatTransfer"></canvas>
                                <!-- 对流换热系数加载动画 -->
                                <div id="heatChartLoading" class="aero-chart-loading">
                                    <div class="aero-loading-spinner"></div>
                                    <div class="aero-loading-text">正在生成对流换热系数分布</div>
                                    <div class="aero-loading-subtitle">AI智能分析中...</div>
                                </div>
                            </div>
                            <!-- 下部分：坐标分布图 -->
                            <div style="flex: 1; position: relative; min-height: 35%; border-top: 1px solid #e5e7eb; padding-top: 8px;">
                                <canvas id="chartHeatChordwise"></canvas>
                                <div style="position: absolute; top: 2px; left: 10px; font-size: 11px; color: #666; font-weight: bold;">
                                    📊 坐标方向分布
                                </div>

                            </div>
                        </div>
                        <!-- 3D对流换热系数容器 -->
                        <div id="heat3DContainer" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; background: #ffffff; overflow: hidden; display: none; border-radius: 8px; z-index: 10;">
                            <div id="heat3DCanvas" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0;"></div>
                            <!-- 3D加载状态指示器 -->
                            <div class="htc-beta-3d-loading" id="heat3DLoading" style="display: none;">
                                <div class="loading-spinner"></div>
                                <div class="loading-text" id="heat3DLoadingText">正在生成3D对流换热系数分布...</div>
                                <div class="loading-progress">
                                    <div class="loading-progress-bar" id="heat3DProgressBar"></div>
                                </div>
                            </div>
                            <!-- 3D控制面板 -->
                            <div class="htc-beta-3d-controls" style="position: absolute; top: 10px; right: 10px; background: rgba(255,255,255,0.9); padding: 10px; border-radius: 5px; font-size: 12px;">
                                <div>🎮 鼠标控制：</div>
                                <div>• 左键拖拽：旋转</div>
                                <div>• 滚轮：缩放</div>
                                <div>• 右键拖拽：平移</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <button class="logout" onclick="location.href='/'">退出登录</button>
            <button class="configure" onclick="location.href='/configure'">STL模型管理</button>
        </div>
    </div>

    <script>
        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // 全局变量用于保存状态
        let stlRendered = false;
        let lastPredictionData = null;
        
        // 3D冰形可视化相关全局变量
        let ice3DScene = null;
        let ice3DCamera = null;
        let ice3DRenderer = null;
        let ice3DControls = null;
        let ice3DMeshes = [];
        let ice3DAnimationId = null;
        let ice3DAutoMove = true; // 自动上下移动开关
        let ice3DMoveSpeed = 0.01; // 上下移动速度
        let ice3DBaseCameraY = 1.31; // 基础Y位置
        let ice3DMoveRange = 2; // 上下移动范围

        // HTC和Beta 3D可视化相关全局变量
        let water3DScene = null;
        let water3DCamera = null;
        let water3DRenderer = null;
        let water3DControls = null;
        let water3DMeshes = [];
        let water3DAnimationId = null;
        let water3DViewActive = false;

        let heat3DScene = null;
        let heat3DCamera = null;
        let heat3DRenderer = null;
        let heat3DControls = null;
        let heat3DMeshes = [];
        let heat3DAnimationId = null;
        let heat3DViewActive = false;

        // 保存HTC和Beta数据
        let lastHTCBetaData = null;

        // 生成测试数据函数
        function generateTestData() {
            console.log('生成测试数据...');

            // 生成模拟的坐标和数值数据
            const numPoints = 100;
            const coordinates = [];
            const htcValues = [];
            const betaValues = [];

            for (let i = 0; i < numPoints; i++) {
                // 生成翼型周围的坐标点
                const angle = (i / numPoints) * 2 * Math.PI;
                const radius = 0.5 + Math.random() * 0.3;
                const x = Math.cos(angle) * radius;
                const y = Math.sin(angle) * radius;
                const z = Math.random() * 0.1;

                coordinates.push([x, y, z]);

                // 生成模拟的HTC值 (100-1000 W/m²·K)
                htcValues.push(100 + Math.random() * 900);

                // 生成模拟的Beta值 (0-1)
                betaValues.push(Math.random());
            }

            // 计算统计信息
            const htcStats = {
                min: Math.min(...htcValues),
                max: Math.max(...htcValues),
                mean: htcValues.reduce((a, b) => a + b) / htcValues.length,
                std: Math.sqrt(htcValues.reduce((a, b) => a + Math.pow(b - htcValues.reduce((c, d) => c + d) / htcValues.length, 2), 0) / htcValues.length)
            };

            const betaStats = {
                min: Math.min(...betaValues),
                max: Math.max(...betaValues),
                mean: betaValues.reduce((a, b) => a + b) / betaValues.length,
                std: Math.sqrt(betaValues.reduce((a, b) => a + Math.pow(b - betaValues.reduce((c, d) => c + d) / betaValues.length, 2), 0) / betaValues.length)
            };

            // 创建测试数据对象
            const testData = {
                success: true,
                htc: {
                    values: htcValues,
                    stats: htcStats,
                    unit: "W/m²·K"
                },
                beta: {
                    values: betaValues,
                    stats: betaStats,
                    unit: "无量纲"
                },
                coordinates: coordinates,
                conditions: {
                    velocity: 100,
                    temperature_k: 268,
                    temperature_c: -5,
                    lwc: 0.5,
                    mvd: 20,
                    time_minutes: 1
                }
            };

            // 保存测试数据并显示
            lastHTCBetaData = testData;
            displayHTCBetaResults(testData);

            showNotification('测试数据已生成，现在可以测试3D交互功能', 'success');
            console.log('测试数据生成完成:', testData);
        }

        // 完全重置3D状态的函数
        function reset3DState(type) {
            if (type === 'water') {
                if (water3DControls) {
                    water3DControls.dispose();
                    water3DControls = null;
                }
                if (water3DRenderer) {
                    water3DRenderer.dispose();
                    water3DRenderer = null;
                }
                if (water3DAnimationId) {
                    cancelAnimationFrame(water3DAnimationId);
                    water3DAnimationId = null;
                }
                water3DMeshes.forEach(mesh => {
                    if (mesh.geometry) mesh.geometry.dispose();
                    if (mesh.material) {
                        if (Array.isArray(mesh.material)) {
                            mesh.material.forEach(mat => mat.dispose());
                        } else {
                            mesh.material.dispose();
                        }
                    }
                });
                water3DMeshes = [];
                water3DScene = null;
                water3DCamera = null;
                console.log('水滴收集系数3D状态已完全重置');
            } else if (type === 'heat') {
                if (heat3DControls) {
                    heat3DControls.dispose();
                    heat3DControls = null;
                }
                if (heat3DRenderer) {
                    heat3DRenderer.dispose();
                    heat3DRenderer = null;
                }
                if (heat3DAnimationId) {
                    cancelAnimationFrame(heat3DAnimationId);
                    heat3DAnimationId = null;
                }
                heat3DMeshes.forEach(mesh => {
                    if (mesh.geometry) mesh.geometry.dispose();
                    if (mesh.material) {
                        if (Array.isArray(mesh.material)) {
                            mesh.material.forEach(mat => mat.dispose());
                        } else {
                            mesh.material.dispose();
                        }
                    }
                });
                heat3DMeshes = [];
                heat3DScene = null;
                heat3DCamera = null;
                console.log('对流换热系数3D状态已完全重置');
            }
        }

        // 切换3D视图函数
        // 切换3D视图函数（带平滑动画）
        function toggle3DView(type) {
            const canvas = document.getElementById(type === 'water' ? 'chartWaterCollection' : 'chartHeatTransfer');
            const container3D = document.getElementById(type === 'water' ? 'water3DContainer' : 'heat3DContainer');

            if (!canvas || !container3D) {
                console.error(`找不到${type}的图表元素`);
                return;
            }

            console.log(`切换${type}视图，当前状态:`, type === 'water' ? water3DViewActive : heat3DViewActive);

            if (type === 'water') {
                water3DViewActive = !water3DViewActive;
                if (water3DViewActive) {
                    // 切换到3D视图（带动画）
                    switchTo3DWithAnimation('water', canvas, container3D);
                } else {
                    // 切换到2D视图（带动画）
                    switchTo2DWithAnimation('water', canvas, container3D);
                }
            } else if (type === 'heat') {
                heat3DViewActive = !heat3DViewActive;
                if (heat3DViewActive) {
                    // 切换到3D视图（带动画）
                    switchTo3DWithAnimation('heat', canvas, container3D);
                } else {
                    // 切换到2D视图（带动画）
                    switchTo2DWithAnimation('heat', canvas, container3D);
                }
            }
        }
        let ice3DMoveDirection = 1; // 移动方向 1向上 -1向下
        
        // 3D冰形加载状态管理
        function show3DIceLoading(message = '正在初始化3D冰形场景...', progress = 0) {
            const loadingElement = document.getElementById('ice3DLoading');
            const loadingText = document.getElementById('ice3DLoadingText');
            const progressBar = document.getElementById('ice3DProgressBar');
            
            if (loadingElement) {
                loadingElement.style.display = 'flex';
                loadingElement.classList.remove('fade-out');
                
                if (loadingText) {
                    loadingText.textContent = message;
                }
                
                if (progressBar) {
                    progressBar.style.width = progress + '%';
                }
                
                console.log('显示3D冰形加载状态:', message, progress + '%');
            }
        }
        
        function hide3DIceLoading() {
            const loadingElement = document.getElementById('ice3DLoading');
            if (loadingElement) {
                loadingElement.classList.add('fade-out');
                setTimeout(() => {
                    loadingElement.style.display = 'none';
                    console.log('隐藏3D冰形加载状态');
                }, 500); // 等待淡出动画完成
            }
        }
        
        function update3DIceProgress(message, progress) {
            const loadingText = document.getElementById('ice3DLoadingText');
            const progressBar = document.getElementById('ice3DProgressBar');
            
            if (loadingText) {
                loadingText.textContent = message;
            }
            
            if (progressBar) {
                progressBar.style.width = progress + '%';
            }
            
            console.log('更新3D冰形加载进度:', message, progress + '%');
        }
        
        // 气动系数图表加载状态管理
        function showAeroChartLoading(chartType, message = '') {
            const loadingId = chartType === 'lift' ? 'liftChartLoading' : 'dragChartLoading';
            const loadingElement = document.getElementById(loadingId);
            
            if (loadingElement) {
                const textElement = loadingElement.querySelector('.aero-loading-text');
                if (message && textElement) {
                    textElement.textContent = message;
                }
                
                loadingElement.style.display = 'flex';
                loadingElement.classList.remove('fade-out');
                console.log(`显示${chartType === 'lift' ? '升力' : '阻力'}系数图表加载状态`);
            }
        }
        
        function hideAeroChartLoading(chartType) {
            const loadingId = chartType === 'lift' ? 'liftChartLoading' : 'dragChartLoading';
            const loadingElement = document.getElementById(loadingId);

            if (loadingElement) {
                loadingElement.classList.add('fade-out');
                setTimeout(() => {
                    loadingElement.style.display = 'none';
                    console.log(`隐藏${chartType === 'lift' ? '升力' : '阻力'}系数图表加载状态`);
                }, 500); // 等待淡出动画完成
            }
        }

        // HTC和Beta图表加载状态管理（使用和气动系数一样的逻辑）
        function showHTCBetaChartLoading(chartType, message = '') {
            const loadingId = chartType === 'water' ? 'waterChartLoading' : 'heatChartLoading';
            const loadingElement = document.getElementById(loadingId);

            if (loadingElement) {
                const textElement = loadingElement.querySelector('.aero-loading-text');
                if (message && textElement) {
                    textElement.textContent = message;
                }

                loadingElement.style.display = 'flex';
                loadingElement.classList.remove('fade-out');
                console.log(`显示${chartType === 'water' ? '水滴收集系数' : '对流换热系数'}图表加载状态`);
            }
        }

        function hideHTCBetaChartLoading(chartType) {
            const loadingId = chartType === 'water' ? 'waterChartLoading' : 'heatChartLoading';
            const loadingElement = document.getElementById(loadingId);

            if (loadingElement) {
                loadingElement.classList.add('fade-out');
                setTimeout(() => {
                    loadingElement.style.display = 'none';
                    console.log(`隐藏${chartType === 'water' ? '水滴收集系数' : '对流换热系数'}图表加载状态`);
                }, 500); // 等待淡出动画完成
            }
        }
        
        window.onload = function() {
            // 页面刷新时显示3D冰形加载框
            show3DIceLoading('正在初始化3D冰形场景...', 10);
            
            // 检查是否有保存的气动系数数据，决定是否显示加载框
            const savedAeroData = localStorage.getItem('aeroCoefficients');
            if (!savedAeroData) {
                // 没有保存数据，显示等待提示，但不立即隐藏加载框
                const liftLoadingText = document.querySelector('#liftChartLoading .aero-loading-text');
                const dragLoadingText = document.querySelector('#dragChartLoading .aero-loading-text');
                const liftLoadingSubtitle = document.querySelector('#liftChartLoading .aero-loading-subtitle');
                const dragLoadingSubtitle = document.querySelector('#dragChartLoading .aero-loading-subtitle');

                if (liftLoadingText) liftLoadingText.textContent = '等待预测数据...';
                if (dragLoadingText) dragLoadingText.textContent = '等待预测数据...';
                if (liftLoadingSubtitle) liftLoadingSubtitle.textContent = '点击"AI智能预测"按钮开始';
                if (dragLoadingSubtitle) dragLoadingSubtitle.textContent = '点击"AI智能预测"按钮开始';

                // 延迟隐藏，与恢复数据流程同步
                setTimeout(() => {
                    hideAeroChartLoading('lift');
                    hideAeroChartLoading('drag');
                }, 4500); // 稍微延迟到恢复流程之后
            } else {
                // 有保存数据，显示恢复提示
                const liftLoadingText = document.querySelector('#liftChartLoading .aero-loading-text');
                const dragLoadingText = document.querySelector('#dragChartLoading .aero-loading-text');
                const liftLoadingSubtitle = document.querySelector('#liftChartLoading .aero-loading-subtitle');
                const dragLoadingSubtitle = document.querySelector('#dragChartLoading .aero-loading-subtitle');

                if (liftLoadingText) liftLoadingText.textContent = '正在恢复上一冰形下的升力系数数据...';
                if (dragLoadingText) dragLoadingText.textContent = '正在恢复上一冰形下的阻力系数数据...';
                //if (liftLoadingSubtitle) liftLoadingSubtitle.textContent = '从本地存储恢复中...';
                //if (dragLoadingSubtitle) dragLoadingSubtitle.textContent = '从本地存储恢复中...';
            }

            // 检查是否有保存的HTC和Beta数据，决定是否显示加载框
            const savedHTCBetaData = localStorage.getItem('htcBetaResults');
            if (!savedHTCBetaData) {
                // 没有保存数据，显示等待提示
                const waterLoadingText = document.querySelector('#waterChartLoading .aero-loading-text');
                const heatLoadingText = document.querySelector('#heatChartLoading .aero-loading-text');
                const waterLoadingSubtitle = document.querySelector('#waterChartLoading .aero-loading-subtitle');
                const heatLoadingSubtitle = document.querySelector('#heatChartLoading .aero-loading-subtitle');

                if (waterLoadingText) waterLoadingText.textContent = '等待预测数据...';
                if (heatLoadingText) heatLoadingText.textContent = '等待预测数据...';
                if (waterLoadingSubtitle) waterLoadingSubtitle.textContent = '点击"AI智能预测"按钮开始';
                if (heatLoadingSubtitle) heatLoadingSubtitle.textContent = '点击"AI智能预测"按钮开始';

                // 延迟隐藏，与恢复数据流程同步
                setTimeout(() => {
                    hideHTCBetaChartLoading('water');
                    hideHTCBetaChartLoading('heat');
                }, 4500); // 稍微延迟到恢复流程之后
            } else {
                // 有保存数据，显示恢复提示
                const waterLoadingText = document.querySelector('#waterChartLoading .aero-loading-text');
                const heatLoadingText = document.querySelector('#heatChartLoading .aero-loading-text');
                const waterLoadingSubtitle = document.querySelector('#waterChartLoading .aero-loading-subtitle');
                const heatLoadingSubtitle = document.querySelector('#heatChartLoading .aero-loading-subtitle');

                if (waterLoadingText) waterLoadingText.textContent = '正在恢复上一冰形下的水滴收集系数数据...';
                if (heatLoadingText) heatLoadingText.textContent = '正在恢复上一冰形下的对流换热系数数据...';
                //if (waterLoadingSubtitle) waterLoadingSubtitle.textContent = '从本地存储恢复中...';
                //if (heatLoadingSubtitle) heatLoadingSubtitle.textContent = '从本地存储恢复中...';
            }
            
            // 验证页面元素
            console.log('=== 页面元素检查 ===');
            const chartElement = document.getElementById('chartIcingShape');
            const displayModule = document.getElementById('displayModule');
            const ice3DContainer = document.getElementById('ice3DContainer');
            
            console.log('chartIcingShape元素:', !!chartElement);
            console.log('displayModule元素:', !!displayModule);
            console.log('ice3DContainer元素:', !!ice3DContainer);
            
            // 列出所有可能的图表相关元素
            const chartElements = document.querySelectorAll('[id*="chart"], [class*="chart"], canvas');
            console.log('页面中的图表相关元素:', Array.from(chartElements).map(el => el.id || el.tagName));
            
            showModule('displayModule');
            
            // 恢复工况参数
            restoreConditions();
            
            // 初始化3D冰形场景
            setTimeout(() => {
                update3DIceProgress('正在初始化3D场景...', 30);
                console.log('页面加载完成，初始化3D冰形场景...');
                init3DIceVisualization();
                
                // 检查是否有保存的预测数据
                setTimeout(() => {
                    update3DIceProgress('检查预测数据...', 60);
                    const savedPrediction = localStorage.getItem('iceShapePrediction');
                    if (!savedPrediction) {
                        console.log('无保存的预测数据，显示空3D场景');
                        update3DIceProgress('场景就绪', 100);
                        setTimeout(() => hide3DIceLoading(), 500);
                    } else {
                        console.log('发现保存的预测数据，将在restorePredictionResults中恢复');
                        update3DIceProgress('恢复预测数据...', 80);
                    }
                }, 500);
            }, 800);
            
            // 恢复预测结果
            setTimeout(() => {
                restorePredictionResults();
            }, 1000);
            
            // 恢复气动系数结果
            setTimeout(() => {
                restoreAeroResults();
            }, 4000);

            // 恢复HTC和Beta结果
            setTimeout(() => {
                restoreHTCBetaResults();
            }, 4500);
            
            // 设置STL动画联动监听器
            setTimeout(() => {
                setupSTLAnimationListeners();
                
                // 如果有保存的工况参数，立即应用动画效果
                const savedConditions = localStorage.getItem('iceShapeConditions');
                if (savedConditions) {
                    try {
                        const conditions = JSON.parse(savedConditions);
                        if (Date.now() - conditions.timestamp < 3600000) { // 1小时内有效
                            setTimeout(() => {
                                updateSTLAnimation(
                                    conditions.velocity, 
                                    conditions.temperature, 
                                    conditions.lwc, 
                                    conditions.mvd
                                );
                            }, 4000); // 等待STL加载完成
                        }
                    } catch (e) {
                        console.log('恢复工况动画效果失败:', e);
                    }
                }
            }, 1500);
            
            // 只有在STL未渲染过的情况下才渲染STL
            if (!stlRendered) {
                // 确保所有依赖加载完成后再渲染STL
                setTimeout(() => {
                    if (typeof THREE !== 'undefined' && 
                        typeof THREE.STLLoader !== 'undefined' && 
                        typeof THREE.OrbitControls !== 'undefined') {
                        console.log('所有依赖已加载，开始渲染STL');
                        renderSTL();
                        stlRendered = true;
                    } else {
                        console.log('依赖未完全加载，等待更长时间...');
                        console.log('THREE:', typeof THREE);
                        console.log('STLLoader:', typeof THREE?.STLLoader);
                        console.log('OrbitControls:', typeof THREE?.OrbitControls);
                        setTimeout(() => {
                            renderSTL();
                            stlRendered = true;
                        }, 2000);
                    }
                }, 1500);
            } else {
                console.log('STL已渲染过，跳过重新渲染');
            }
        };
        
        // 保存工况参数到localStorage
        function saveConditions(velocity, temperature, lwc, mvd, height, aoa) {
            const conditions = {
                velocity: velocity,
                temperature: temperature,
                lwc: lwc,
                mvd: mvd,
                height: height,
                aoa: aoa,
                timestamp: Date.now()
            };
            localStorage.setItem('iceShapeConditions', JSON.stringify(conditions));
        }
        
        // 更新STL模型动画效果
        function updateSTLAnimation(velocity, temperature, lwc, mvd) {
            console.log('🎬 调用updateSTLAnimation:', { velocity, temperature, lwc, mvd });
            
            if (!stlMesh || !stlControls) {
                console.log('⚠️ STL模型或控制器未就绪，跳过动画更新');
                return;
            }
            
            // 解析数值，确保为数字类型
            const v = parseFloat(velocity) || 0;
            const t = parseFloat(temperature) || 273;
            const l = parseFloat(lwc) || 0;
            const m = parseFloat(mvd) || 0;
            
            console.log('🎬 更新STL动画效果:', { velocity: v, temperature: t, lwc: l, mvd: m });
            
            // 显示动画状态指示器（当有任何非默认值时显示）
            const statusPanel = document.getElementById('stlAnimationStatus');
            if (statusPanel) {
                const hasActiveParams = (v > 0 || Math.abs(t - 273) > 1 || l > 0 || m > 0);
                if (hasActiveParams) {
                    statusPanel.style.display = 'block';
                    console.log('✅ 显示动画状态指示器');
                } else {
                    console.log('💡 参数为默认值，保持状态指示器隐藏');
                }
            }
            
            // 1. 速度影响旋转速度 (速度越大，旋转越快)
            if (v > 0) {
                stlAnimationSpeed = Math.min(v / 50, 3.0); // 速度范围0-150m/s映射到0-3倍速
                stlControls.autoRotateSpeed = stlAnimationSpeed;
                console.log(`✈️ 飞行速度: ${v} m/s -> 旋转速度: ${stlAnimationSpeed.toFixed(2)}倍`);
                
                // 更新状态显示
                const speedEl = document.getElementById('animationSpeed');
                if (speedEl) {
                    speedEl.textContent = `${stlAnimationSpeed.toFixed(1)}x`;
                    speedEl.style.color = v > 100 ? '#dc3545' : v > 50 ? '#ffc107' : '#007bff';
                }
            } else {
                stlAnimationSpeed = 1.0; // 重置为默认速度
                stlControls.autoRotateSpeed = stlAnimationSpeed;
                const speedEl = document.getElementById('animationSpeed');
                if (speedEl) {
                    speedEl.textContent = `1.0x`;
                    speedEl.style.color = '#6c757d';
                }
            }
            
            // 2. 温度影响颜色 (低温偏蓝，高温偏红)
            if (stlMesh.material && t > 0) {
                const tempCelsius = t - 273.15; // 转换为摄氏度
                let hue = 0.6; // 默认蓝色
                let tempDesc = '标准';
                
                if (tempCelsius < -20) {
                    hue = 0.7; // 深蓝 (极冷)
                    tempDesc = '极冷';
                } else if (tempCelsius < 0) {
                    hue = 0.55 + (tempCelsius + 20) * 0.0075; // -20°C到0°C: 深蓝到浅蓝
                    tempDesc = '冰冻';
                } else if (tempCelsius < 20) {
                    hue = 0.3 + tempCelsius * 0.0125; // 0°C到20°C: 绿到黄
                    tempDesc = '正常';
                } else {
                    hue = 0.05; // 红色 (温暖)
                    tempDesc = '温暖';
                }
                
                // 更新材质颜色
                if (stlMesh.material.color) {
                    stlMesh.material.color.setHSL(hue, 0.8, 0.6);
                    console.log(`🌡️ 温度: ${tempCelsius.toFixed(1)}°C -> 色调: ${hue.toFixed(2)}`);
                }
                
                // 更新状态显示
                const tempEl = document.getElementById('animationTemp');
                if (tempEl) {
                    tempEl.textContent = `${tempDesc}`;
                    tempEl.style.color = tempCelsius < -10 ? '#007bff' : tempCelsius > 15 ? '#dc3545' : '#28a745';
                }
            }
            
            // 3. 液态水含量影响"湍流"效果 (LWC越高，飞机晃动越明显)
            if (l > 0) {
                stlTurbulenceEffect = Math.min(l / 2.0, 1.0); // LWC 0-2映射到0-1
                console.log(`💧 液态水含量: ${l} g/m³ -> 湍流强度: ${stlTurbulenceEffect.toFixed(2)}`);
                
                // 更新状态显示
                const turbEl = document.getElementById('animationTurbulence');
                if (turbEl) {
                    const turbDesc = stlTurbulenceEffect > 0.7 ? '强烈' : stlTurbulenceEffect > 0.3 ? '中等' : '轻微';
                    turbEl.textContent = `${turbDesc}`;
                    turbEl.style.color = stlTurbulenceEffect > 0.7 ? '#dc3545' : stlTurbulenceEffect > 0.3 ? '#ffc107' : '#28a745';
                }
            } else {
                stlTurbulenceEffect = 0;
                const turbEl = document.getElementById('animationTurbulence');
                if (turbEl) {
                    turbEl.textContent = `无`;
                    turbEl.style.color = '#6c757d';
                }
            }
            
            // 4. 水滴直径影响"结冰严重程度"视觉效果
            if (m > 0) {
                stlIcingEffect = Math.min(m / 50, 1.0); // MVD 0-50μm映射到0-1
                console.log(`🧊 水滴直径: ${m} μm -> 结冰效果: ${stlIcingEffect.toFixed(2)}`);
                
                // 结冰效果：降低材质光泽度
                if (stlMesh.material && stlMesh.material.shininess !== undefined) {
                    stlMesh.material.shininess = 200 * (1 - stlIcingEffect * 0.7); // 结冰时降低光泽
                }
                
                // 显示冰点粒子效果
                if (iceParticleSystem) {
                    iceParticleSystem.visible = true;
                    iceParticleSystem.material.opacity = stlIcingEffect * 0.8;
                    console.log(`❄️ 显示冰点粒子，强度: ${stlIcingEffect.toFixed(2)}`);
                }
                
                // 应用霜冻效果
                updateFrostEffect(stlIcingEffect);
                
                // 更新状态显示
                const iceEl = document.getElementById('animationIcing');
                if (iceEl) {
                    const iceDesc = stlIcingEffect > 0.7 ? '严重❄️' : stlIcingEffect > 0.3 ? '中等🧊' : '轻微💎';
                    iceEl.textContent = `${iceDesc}`;
                    iceEl.style.color = stlIcingEffect > 0.7 ? '#6f42c1' : stlIcingEffect > 0.3 ? '#e83e8c' : '#17a2b8';
                }
            } else {
                stlIcingEffect = 0;
                // 恢复默认光泽度
                if (stlMesh.material && stlMesh.material.shininess !== undefined) {
                    stlMesh.material.shininess = 200;
                }
                
                // 隐藏冰点粒子效果
                if (iceParticleSystem) {
                    iceParticleSystem.visible = false;
                    console.log('🚫 隐藏冰点粒子');
                }
                
                // 清除霜冻效果
                updateFrostEffect(0);
                
                const iceEl = document.getElementById('animationIcing');
                if (iceEl) {
                    iceEl.textContent = `无`;
                    iceEl.style.color = '#6c757d';
                }
            }
            
            // 5. 新增：气流可视化效果
            if (v > 10) { // 速度大于10m/s时显示气流
                if (airflowParticleSystem) {
                    airflowParticleSystem.visible = true;
                    airflowParticleSystem.material.opacity = Math.min(v / 100, 0.8);
                    console.log(`💨 显示气流效果，强度: ${(v / 100).toFixed(2)}`);
                }
            } else {
                if (airflowParticleSystem) {
                    airflowParticleSystem.visible = false;
                    console.log('🚫 隐藏气流效果');
                }
            }
            
            // 6. 新增：更新环境光照
            if (environmentLights.sun) {
                updateEnvironmentLights(v, t, Date.now());
                console.log('💡 更新环境光照');
            }
            
            // 7. 新增：更新飞行姿态
            updateFlightAttitude(v, stlTurbulenceEffect);
            console.log('✈️ 更新飞行姿态');
        }
        
        // 监听输入框变化，实时更新动画
        function setupSTLAnimationListeners() {
            const inputs = ['velocity', 'temperature', 'lwc', 'mvd', 'height', 'aoa'];
            
            inputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('input', function() {
                        const velocity = document.getElementById('velocity').value;
                        const temperature = document.getElementById('temperature').value;
                        const lwc = document.getElementById('lwc').value;
                        const mvd = document.getElementById('mvd').value;
                        
                        // 实时更新动画效果
                        updateSTLAnimation(velocity, temperature, lwc, mvd);
                    });
                    
                    // 鼠标离开时也更新一次
                    input.addEventListener('blur', function() {
                        const velocity = document.getElementById('velocity').value;
                        const temperature = document.getElementById('temperature').value;
                        const lwc = document.getElementById('lwc').value;
                        const mvd = document.getElementById('mvd').value;
                        
                        updateSTLAnimation(velocity, temperature, lwc, mvd);
                    });
                }
            });
            
            console.log('🎮 STL动画联动监听器已设置');
        }
        
        // 创建冰点粒子效果
        function createIceParticleSystem(scene) {
            if (!scene) return null;
            
            console.log('🧊 创建冰点粒子系统...');
            
            // 创建粒子几何体
            const particleCount = 200;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);
            const velocities = new Float32Array(particleCount * 3);
            const sizes = new Float32Array(particleCount);
            const colors = new Float32Array(particleCount * 3);
            
            // 初始化粒子位置、速度、大小和颜色
            for (let i = 0; i < particleCount; i++) {
                const i3 = i * 3;
                
                // 随机位置（围绕飞机模型）
                positions[i3] = (Math.random() - 0.5) * 30;     // x
                positions[i3 + 1] = (Math.random() - 0.5) * 20; // y
                positions[i3 + 2] = (Math.random() - 0.5) * 15; // z
                
                // 随机速度（向下飘落）
                velocities[i3] = (Math.random() - 0.5) * 0.1;     // x方向轻微漂移
                velocities[i3 + 1] = -Math.random() * 0.2 - 0.1;  // y方向向下
                velocities[i3 + 2] = (Math.random() - 0.5) * 0.1; // z方向轻微漂移
                
                // 随机大小
                sizes[i] = Math.random() * 1.5 + 0.3; // 从Math.random() * 3 + 1改为更小的尺寸
                
                // 冰晶颜色（白色到浅蓝色）
                const iceColor = new THREE.Color();
                iceColor.setHSL(0.55 + Math.random() * 0.1, 0.3 + Math.random() * 0.3, 0.8 + Math.random() * 0.2);
                colors[i3] = iceColor.r;
                colors[i3 + 1] = iceColor.g;
                colors[i3 + 2] = iceColor.b;
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            
            // 创建粒子材质
            const material = new THREE.PointsMaterial({
                size: 0.2, // 从2减小到0.8，更小的冰点粒子
                vertexColors: true,
                transparent: true,
                opacity: 0.6, // 从0.8减小到0.6，更透明
                alphaTest: 0.1,
                blending: THREE.AdditiveBlending
            });
            
            // 创建粒子系统
            const particles = new THREE.Points(geometry, material);
            particles.name = 'iceParticles';
            particles.visible = false; // 初始隐藏
            
            // 保存速度信息到userData
            particles.userData.velocities = velocities;
            particles.userData.originalPositions = positions.slice(); // 备份原始位置
            
            scene.add(particles);
            console.log('✅ 冰点粒子系统已创建');
            
            return particles;
        }
        
        // 更新冰点粒子动画
        function updateIceParticles() {
            if (!iceParticleSystem) return;
            
            const positions = iceParticleSystem.geometry.attributes.position.array;
            const velocities = iceParticleSystem.userData.velocities;
            const originalPositions = iceParticleSystem.userData.originalPositions;
            
            for (let i = 0; i < positions.length; i += 3) {
                // 更新位置
                positions[i] += velocities[i];         // x
                positions[i + 1] += velocities[i + 1]; // y
                positions[i + 2] += velocities[i + 2]; // z
                
                // 如果粒子掉出范围，重置到顶部
                if (positions[i + 1] < -15) {
                    positions[i] = originalPositions[i] + (Math.random() - 0.5) * 5;
                    positions[i + 1] = 15;
                    positions[i + 2] = originalPositions[i + 2] + (Math.random() - 0.5) * 5;
                }
                
                // 添加轻微的随机扰动（模拟风吹效果）
                if (stlTurbulenceEffect > 0) {
                    const time = Date.now() * 0.001;
                    positions[i] += Math.sin(time + i * 0.1) * stlTurbulenceEffect * 0.01;
                    positions[i + 2] += Math.cos(time + i * 0.1) * stlTurbulenceEffect * 0.01;
                }
            }
            
            iceParticleSystem.geometry.attributes.position.needsUpdate = true;
        }
        
        // 创建霜冻效果
        function createFrostEffect(mesh) {
            if (!mesh || !mesh.material) return;
            
            console.log('❄️ 创建霜冻效果...');
            
            // 为模型添加霜冻纹理覆盖层
            const originalMaterial = mesh.material;
            
            // 创建霜冻材质
            const frostMaterial = new THREE.MeshPhongMaterial({
                color: 0xf0f8ff, // 淡蓝白色
                transparent: true,
                opacity: 0,
                shininess: 300,
                specular: 0xffffff
            });
            
            // 如果是多材质，需要特殊处理
            if (Array.isArray(originalMaterial)) {
                mesh.material = originalMaterial.map(mat => {
                    return new THREE.MeshPhongMaterial({
                        color: mat.color,
                        vertexColors: mat.vertexColors,
                        transparent: true,
                        opacity: mat.opacity,
                        shininess: mat.shininess,
                        specular: mat.specular
                    });
                });
            } else {
                // 保持原有的顶点颜色和属性
                mesh.material = new THREE.MeshPhongMaterial({
                    vertexColors: originalMaterial.vertexColors,
                    transparent: true,
                    opacity: originalMaterial.opacity,
                    shininess: originalMaterial.shininess,
                    specular: originalMaterial.specular,
                    side: originalMaterial.side
                });
            }
            
            console.log('✅ 霜冻效果材质已准备');
        }
        
        // 更新霜冻效果
        function updateFrostEffect(intensity) {
            if (!stlMesh || !stlMesh.material) return;
            
            // 根据结冰强度调整霜冻效果
            if (Array.isArray(stlMesh.material)) {
                stlMesh.material.forEach(mat => {
                    if (mat.transparent) {
                        // 增加白色霜冻覆盖
                        const frostColor = new THREE.Color(0xf0f8ff);
                        mat.color.lerp(frostColor, intensity * 0.3);
                        mat.shininess = 300 - intensity * 100; // 增加光泽模拟冰面
                    }
                });
            } else {
                // 单材质处理
                const frostColor = new THREE.Color(0xf0f8ff);
                const originalColor = new THREE.Color();
                
                // 根据强度混合霜冻颜色
                if (intensity > 0) {
                    originalColor.setHSL(0.6, 0.8, 0.6); // 基础蓝色
                    stlMesh.material.color.lerpColors(originalColor, frostColor, intensity * 0.4);
                    stlMesh.material.shininess = 200 + intensity * 100; // 增加光泽
                    stlMesh.material.specular = new THREE.Color(0xffffff).multiplyScalar(intensity);
                }
            }
        }
        
        // 创建气流可视化粒子系统
        function createAirflowParticleSystem(scene) {
            if (!scene) return null;
            
            console.log('💨 创建气流可视化粒子系统...');
            
            const particleCount = 300;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);
            const velocities = new Float32Array(particleCount * 3);
            const sizes = new Float32Array(particleCount);
            const colors = new Float32Array(particleCount * 3);
            
            // 初始化气流粒子
            for (let i = 0; i < particleCount; i++) {
                const i3 = i * 3;
                
                // 粒子从飞机前方和周围产生
                positions[i3] = (Math.random() - 0.5) * 25 - 10;     // x (从前方开始)
                positions[i3 + 1] = (Math.random() - 0.5) * 15;     // y
                positions[i3 + 2] = (Math.random() - 0.5) * 20;     // z
                
                // 气流速度（从前向后流动）
                velocities[i3] = Math.random() * 0.3 + 0.2;         // x方向向后
                velocities[i3 + 1] = (Math.random() - 0.5) * 0.1;   // y方向轻微扰动
                velocities[i3 + 2] = (Math.random() - 0.5) * 0.1;   // z方向轻微扰动
                
                // 粒子大小
                sizes[i] = Math.random() * 1.0 + 0.2; // 从Math.random() * 2 + 0.5改为更小的尺寸
                
                // 气流颜色（淡蓝到白色）
                const airColor = new THREE.Color();
                airColor.setHSL(0.55 + Math.random() * 0.1, 0.2 + Math.random() * 0.2, 0.9 + Math.random() * 0.1);
                colors[i3] = airColor.r;
                colors[i3 + 1] = airColor.g;
                colors[i3 + 2] = airColor.b;
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            
            const material = new THREE.PointsMaterial({
                size: 0.2, // 从1.5减小到0.5，更精细的气流粒子
                vertexColors: true,
                transparent: true,
                opacity: 0.4, // 从0.4减小到0.3，更透明
                alphaTest: 0.1,
                blending: THREE.AdditiveBlending
            });
            
            const airflow = new THREE.Points(geometry, material);
            airflow.name = 'airflowParticles';
            airflow.visible = false; // 初始隐藏
            airflow.userData.velocities = velocities;
            airflow.userData.originalPositions = positions.slice();
            
            scene.add(airflow);
            console.log('✅ 气流可视化粒子系统已创建');
            
            return airflow;
        }
        
        // 更新气流粒子动画
        function updateAirflowParticles() {
            if (!airflowParticleSystem) return;
            
            const positions = airflowParticleSystem.geometry.attributes.position.array;
            const velocities = airflowParticleSystem.userData.velocities;
            const originalPositions = airflowParticleSystem.userData.originalPositions;
            
            for (let i = 0; i < positions.length; i += 3) {
                // 根据速度更新位置
                const speedMultiplier = stlAnimationSpeed * 0.5;
                positions[i] += velocities[i] * speedMultiplier;         // x
                positions[i + 1] += velocities[i + 1] * speedMultiplier; // y
                positions[i + 2] += velocities[i + 2] * speedMultiplier; // z
                
                // 湍流效果
                if (stlTurbulenceEffect > 0) {
                    const time = Date.now() * 0.002;
                    positions[i + 1] += Math.sin(time + i * 0.1) * stlTurbulenceEffect * 0.02;
                    positions[i + 2] += Math.cos(time + i * 0.1) * stlTurbulenceEffect * 0.02;
                }
                
                // 如果粒子流出范围，重置到前方
                if (positions[i] > 15) {
                    positions[i] = originalPositions[i] - 10 + (Math.random() - 0.5) * 5;
                    positions[i + 1] = originalPositions[i + 1] + (Math.random() - 0.5) * 3;
                    positions[i + 2] = originalPositions[i + 2] + (Math.random() - 0.5) * 3;
                }
            }
            
            airflowParticleSystem.geometry.attributes.position.needsUpdate = true;
        }
        
        // 创建环境光照系统
        function createEnvironmentLights(scene) {
            if (!scene) return {};
            
            console.log('💡 创建增强环境光照系统...');
            
            // 移除现有的简单光照
            const existingLights = scene.children.filter(child => 
                child instanceof THREE.Light && child.type !== 'HemisphereLight'
            );
            existingLights.forEach(light => scene.remove(light));
            
            const lights = {};
            
            // 主光源（太阳光）
            lights.sun = new THREE.DirectionalLight(0xffffff, 1.2);
            lights.sun.position.set(10, 10, 5);
            lights.sun.castShadow = true;
            lights.sun.shadow.mapSize.width = 2048;
            lights.sun.shadow.mapSize.height = 2048;
            lights.sun.shadow.camera.near = 0.5;
            lights.sun.shadow.camera.far = 500;
            scene.add(lights.sun);
            
            // 环境光（天空光）
            lights.hemisphere = new THREE.HemisphereLight(0x87CEEB, 0x362D1D, 0.6);
            scene.add(lights.hemisphere);
            
            // 补光（从下方）
            lights.fill = new THREE.DirectionalLight(0x87CEEB, 0.3);
            lights.fill.position.set(-5, -5, 0);
            scene.add(lights.fill);
            
            // 边缘光（轮廓）
            lights.rim = new THREE.DirectionalLight(0xffffff, 0.5);
            lights.rim.position.set(-10, 0, -10);
            scene.add(lights.rim);
            
            console.log('✅ 环境光照系统已创建');
            
            return lights;
        }
        
        // 更新环境光照
        function updateEnvironmentLights(velocity, temperature, time) {
            if (!environmentLights.sun) return;
            
            // 根据速度调整光照强度（模拟高速飞行时的光照变化）
            const speedFactor = Math.min(velocity / 100, 2.0);
            environmentLights.sun.intensity = 1.2 + speedFactor * 0.3;
            
            // 根据温度调整色温
            const tempCelsius = temperature - 273.15;
            let lightColor = new THREE.Color();
            if (tempCelsius < -10) {
                lightColor.setHSL(0.6, 0.2, 1.0); // 冷蓝色
            } else if (tempCelsius > 20) {
                lightColor.setHSL(0.1, 0.3, 1.0); // 暖黄色
            } else {
                lightColor.setHSL(0.0, 0.0, 1.0); // 中性白色
            }
            environmentLights.sun.color = lightColor;
            
            // 动态光照位置（模拟太阳移动）
            const lightTime = time * 0.0005;
            environmentLights.sun.position.x = Math.cos(lightTime) * 10;
            environmentLights.sun.position.y = Math.sin(lightTime * 0.5) * 5 + 10;
        }
        
        // 添加飞行姿态控制
        function updateFlightAttitude(velocity, turbulence) {
            if (!stlMesh) return;
            
            const time = Date.now() * 0.001;
            
            // 基于速度的姿态变化
            const speedFactor = Math.min(velocity / 100, 1.0);
            
            // 俯仰角（速度高时机头稍微上扬）
            stlPitchAngle = speedFactor * 0.1 + Math.sin(time * 0.3) * 0.05;
            
            // 偏航角（湍流效果）
            if (turbulence > 0) {
                stlYawAngle = Math.sin(time * 2.0) * turbulence * 0.2;
                stlRollAngle = Math.cos(time * 1.5) * turbulence * 0.1;
            } else {
                stlYawAngle = 0;
                stlRollAngle = 0;
            }
            
            // 应用旋转（保持原有的自动旋转，只添加姿态变化）
            if (stlMesh.userData && stlMesh.userData.originalRotation) {
                // 基于原始旋转应用额外的姿态变化
                stlMesh.rotation.x = stlMesh.userData.originalRotation.x + stlPitchAngle;
                stlMesh.rotation.y = stlMesh.userData.originalRotation.y + stlYawAngle;
                stlMesh.rotation.z = stlMesh.userData.originalRotation.z + stlRollAngle;
            } else {
                // 保存原始旋转状态
                if (!stlMesh.userData) stlMesh.userData = {};
                stlMesh.userData.originalRotation = {
                    x: stlMesh.rotation.x,
                    y: stlMesh.rotation.y,
                    z: stlMesh.rotation.z
                };
            }
        }
        
        // 恢复工况参数
        function restoreConditions() {
            const savedConditions = localStorage.getItem('iceShapeConditions');
            if (savedConditions) {
                try {
                    const conditions = JSON.parse(savedConditions);
                    // 检查是否在1小时内保存的数据
                    if (Date.now() - conditions.timestamp < 3600000) {
                        document.getElementById('velocity').value = conditions.velocity || '';
                        document.getElementById('temperature').value = conditions.temperature || '';
                        document.getElementById('lwc').value = conditions.lwc || '';
                        document.getElementById('mvd').value = conditions.mvd || '';
                        document.getElementById('height').value = conditions.height || '0';
                        document.getElementById('aoa').value = conditions.aoa || '0';
                        console.log('已恢复工况参数');
                    }
                } catch (e) {
                    console.log('恢复工况参数失败:', e);
                }
            }
        }
        
        // 保存预测结果到localStorage
        function savePredictionResults(data) {
            const predictionData = {
                data: data,
                timestamp: Date.now()
            };
            localStorage.setItem('iceShapePrediction', JSON.stringify(predictionData));
            lastPredictionData = data;
        }
        
        // 保存气动系数结果到localStorage
        function saveAeroResults(aeroData) {
            const aeroResultData = {
                data: aeroData,
                timestamp: Date.now()
            };
            localStorage.setItem('aeroCoefficients', JSON.stringify(aeroResultData));
            console.log('已保存气动系数数据到localStorage');
        }

        // 保存HTC和Beta结果到localStorage
        function saveHTCBetaResults(htcBetaData) {
            const htcBetaResultData = {
                data: htcBetaData,
                timestamp: Date.now()
            };
            localStorage.setItem('htcBetaResults', JSON.stringify(htcBetaResultData));
            console.log('已保存HTC和Beta数据到localStorage');
        }
        
        // 恢复预测结果
        function restorePredictionResults() {
            const savedPrediction = localStorage.getItem('iceShapePrediction');
            if (savedPrediction) {
                try {
                    const predictionData = JSON.parse(savedPrediction);
                    // 检查是否在1小时内保存的数据
                    if (Date.now() - predictionData.timestamp < 3600000) {
                        lastPredictionData = predictionData.data;
                        // 延迟一点时间确保DOM完全加载
                        setTimeout(() => {
                            displayIceShapeResults(lastPredictionData);
                            
                            // 恢复3D冰形渲染（场景已在页面加载时初始化）
                            setTimeout(() => {
                                console.log('恢复3D冰形渲染（页面刷新数据恢复）...');
                                update3DIceProgress('恢复3D冰形数据...', 90);
                                
                                if (ice3DScene) {
                                    render3DIcePrediction(lastPredictionData, false);
                                    update3DIceProgress('3D冰形恢复完成', 100);
                                    setTimeout(() => hide3DIceLoading(), 500);
                                } else {
                                    console.log('3D场景尚未初始化完成，稍后重试...');
                                    setTimeout(() => {
                                        if (ice3DScene) {
                                            render3DIcePrediction(lastPredictionData, false);
                                            update3DIceProgress('3D冰形恢复完成', 100);
                                            setTimeout(() => hide3DIceLoading(), 500);
                                        } else {
                                            console.log('3D场景初始化失败，隐藏加载框');
                                            hide3DIceLoading();
                                        }
                                    }, 1000);
                                }
                            }, 800);
                            
                            console.log('已恢复预测结果包括3D冰形');
                        }, 500);
                    }
                } catch (e) {
                    console.log('恢复预测结果失败:', e);
                }
            }
        }
        
        // 恢复气动系数结果
        function restoreAeroResults() {
            const savedAero = localStorage.getItem('aeroCoefficients');
            if (savedAero) {
                try {
                    const aeroResultData = JSON.parse(savedAero);
                    // 检查是否在1小时内保存的数据
                    if (Date.now() - aeroResultData.timestamp < 3600000) {
                        console.log('发现保存的气动系数数据，开始恢复...');
                        
                        // 显示加载动画
                        showAeroChartLoading('lift', '正在恢复上一冰形下的升力系数数据');
                        showAeroChartLoading('drag', '正在恢复上一冰形下的阻力系数数据');
                        
                        // 延迟一点时间确保DOM和Chart.js完全加载
                        setTimeout(() => {
                            displayAeroResults(aeroResultData.data);
                            console.log('已恢复气动系数结果');
                        }, 1500); // 稍微延迟以确保所有依赖加载完成
                    }
                } catch (e) {
                    console.log('恢复气动系数结果失败:', e);
                    hideAeroChartLoading('lift');
                    hideAeroChartLoading('drag');
                }
            }
        }

        // 恢复HTC和Beta结果
        function restoreHTCBetaResults() {
            const savedHTCBeta = localStorage.getItem('htcBetaResults');
            if (savedHTCBeta) {
                try {
                    const htcBetaResultData = JSON.parse(savedHTCBeta);
                    // 检查是否在1小时内保存的数据
                    if (Date.now() - htcBetaResultData.timestamp < 3600000) {
                        console.log('发现保存的HTC和Beta数据，开始恢复...');

                        // 显示加载动画
                        showHTCBetaChartLoading('water', '正在恢复上一冰形下的水滴收集系数数据');
                        showHTCBetaChartLoading('heat', '正在恢复上一冰形下的对流换热系数数据');

                        // 延迟一点时间确保DOM和Chart.js完全加载
                        setTimeout(() => {
                            displayHTCBetaResults(htcBetaResultData.data);
                            console.log('已恢复HTC和Beta结果');

                            // 预加载3D场景，但不显示
                            if (lastHTCBetaData) {
                                init3DWaterVisualizationBackground();
                                init3DHeatVisualizationBackground();
                            }
                        }, 2000); // 稍微延迟以确保所有依赖加载完成
                    }
                } catch (e) {
                    console.log('恢复HTC和Beta结果失败:', e);
                    hideHTCBetaChartLoading('water');
                    hideHTCBetaChartLoading('heat');
                }
            }
        }

        function predictIceShape() {
            const velocity = document.getElementById('velocity').value;
            const temperature = document.getElementById('temperature').value;
            const lwc = document.getElementById('lwc').value;
            const mvd = document.getElementById('mvd').value;
            const height = document.getElementById('height').value;
            const aoa = document.getElementById('aoa').value;

            console.log('🔮 开始AI智能预测，工况参数:', { velocity, temperature, lwc, mvd, height, aoa });

            // 更新预测次数计数器
            updatePredictionCount();

            // 清除之前的气动系数动画定时器
            if (window.liftAnimationTimeout) {
                clearTimeout(window.liftAnimationTimeout);
                window.liftAnimationTimeout = null;
            }
            if (window.dragAnimationTimeout) {
                clearTimeout(window.dragAnimationTimeout);
                window.dragAnimationTimeout = null;
            }

            // 保存工况参数到localStorage
            saveConditions(velocity, temperature, lwc, mvd, height, aoa);

            // 实时更新STL动画效果
            console.log('🎬 预测时触发动画联动...');
            updateSTLAnimation(velocity, temperature, lwc, mvd);

            // 清除旧的误差分析数据
            clearErrorAnalysis();

            // 存储工况到后端会话
            fetch('/set_conditions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    Velocity: parseFloat(velocity),
                    Temperature: parseFloat(temperature),
                    LWC: parseFloat(lwc),
                    MVD: parseFloat(mvd),
                    Height: parseFloat(height),
                    AOA: parseFloat(aoa)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('工况已存储到后端会话');
                } else {
                    console.error('存储工况失败:', data.error);
                }
            })
            .catch(error => console.error('存储工况失败:', error));

            // 执行预测逻辑
            fetch('/predict_ice_shape', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    Velocity: parseFloat(velocity),
                    Temperature: parseFloat(temperature),
                    LWC: parseFloat(lwc),
                    MVD: parseFloat(mvd),
                    Height: parseFloat(height),
                    AOA: parseFloat(aoa),
                    DeltaT: 60
                })
            })
            .then(response => response.json())
            .then((data) => {
                if (data.success) {
                    // 保存预测结果
                    savePredictionResults(data);
                    
                    // 安全地获取图表canvas元素
                    let chartElement = document.getElementById('chartIcingShape');
                    if (!chartElement) {
                        console.warn('找不到chartIcingShape元素，尝试创建...');
                        
                        // 优先在ice2DContainer中创建2D图表
                        const ice2DContainer = document.getElementById('ice2DContainer');
                        const possibleContainers = [
                            ice2DContainer,
                            document.getElementById('displayModule'),
                            document.querySelector('.chart-container'),
                            document.querySelector('.ice-shape-container'),
                            document.body
                        ].filter(Boolean);
                        
                        if (possibleContainers.length > 0) {
                            chartElement = document.createElement('canvas');
                            chartElement.id = 'chartIcingShape';
                            
                            // 如果是在ice2DContainer中创建，先清空占位内容并获取实际尺寸
                            if (possibleContainers[0] === ice2DContainer) {
                                const placeholder = ice2DContainer.querySelector('.ice-2d-placeholder');
                                if (placeholder) {
                                    placeholder.remove();
                                }
                                
                                // 获取ice2DContainer的实际计算尺寸
                                const containerRect = ice2DContainer.getBoundingClientRect();
                                const containerStyle = window.getComputedStyle(ice2DContainer);
                                const borderWidth = parseInt(containerStyle.borderLeftWidth) + parseInt(containerStyle.borderRightWidth);
                                const paddingWidth = parseInt(containerStyle.paddingLeft) + parseInt(containerStyle.paddingRight);
                                const borderHeight = parseInt(containerStyle.borderTopWidth) + parseInt(containerStyle.borderBottomWidth);
                                const paddingHeight = parseInt(containerStyle.paddingTop) + parseInt(containerStyle.paddingBottom);
                                
                                // 计算Canvas的实际可用尺寸
                                let canvasWidth = containerRect.width - borderWidth - paddingWidth;
                                let canvasHeight = containerRect.height - borderHeight - paddingHeight;

                                // 优化长屏幕显示：确保合适的宽高比
                                const aspectRatio = canvasWidth / canvasHeight;
                                const idealAspectRatio = 2.5; // 理想的宽高比，适合翼型显示

                                if (aspectRatio > idealAspectRatio) {
                                    // 屏幕太宽，限制宽度保持比例
                                    const maxWidth = canvasHeight * idealAspectRatio;
                                    canvasWidth = Math.min(canvasWidth, maxWidth);
                                } else if (aspectRatio < 1.8) {
                                    // 屏幕太窄，限制高度保持比例
                                    const maxHeight = canvasWidth / 1.8;
                                    canvasHeight = Math.min(canvasHeight, maxHeight);
                                }

                                // 设置Canvas样式和尺寸，居中显示
                                chartElement.style.width = canvasWidth + 'px';
                                chartElement.style.height = canvasHeight + 'px';
                                chartElement.style.position = 'absolute';
                                chartElement.style.top = '50%';
                                chartElement.style.left = '50%';
                                chartElement.style.transform = 'translate(-50%, -50%)';
                                chartElement.style.borderRadius = '8px';
                                chartElement.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';

                                // 设置Canvas的实际分辨率（devicePixelRatio适配）
                                const devicePixelRatio = window.devicePixelRatio || 1;
                                chartElement.width = canvasWidth * devicePixelRatio;
                                chartElement.height = canvasHeight * devicePixelRatio;

                                // 重要：调整Canvas上下文缩放以确保清晰度
                                const ctx = chartElement.getContext('2d');
                                ctx.scale(devicePixelRatio, devicePixelRatio);

                                ice2DContainer.appendChild(chartElement);
                                console.log('已在ice2DContainer中创建chartIcingShape元素，尺寸:', canvasWidth + 'x' + canvasHeight, 'DPR:', devicePixelRatio);
                            } else {
                                // 其他容器的处理保持原样
                                chartElement.style.width = '100%';
                                chartElement.style.height = '100%';
                                chartElement.style.position = 'absolute';
                                chartElement.style.top = '0';
                                chartElement.style.left = '0';
                                
                                possibleContainers[0].appendChild(chartElement);
                                console.log('已创建chartIcingShape元素');
                            }
                        } else {
                            console.error('无法找到合适的容器来创建图表元素');
                            return;
                        }
                    }
                    
                    const ctx = chartElement.getContext('2d');
                    if (!ctx) {
                        console.error('无法获取canvas上下文');
                        return;
                    }

                    // 如果已有图表，先销毁
                    if (window.iceShapeChart) {
                        window.iceShapeChart.destroy();
                    }

                    // 过滤前缘部分数据用于干净机翼显示
                    const filterLeadingEdge = (shape, threshold = 0.035) => {
                        return shape.filter(point => point[0] <= threshold);
                    };

                    // 所有数据都使用完整轮廓，通过图表显示范围来控制可见区域
                    const cleanBody = filterLeadingEdge(data.clean_body).map(point => ({ x: point[0], y: point[1] }));
                    const initialShape = data.initial_shape.map(point => ({ x: point[0], y: point[1] })); // 完整实验冰形轮廓
                    const predictedShape = data.predicted_shape.map(point => ({ x: point[0], y: point[1] })); // 完整预测冰形轮廓

                    // 初始化图表（增强版）
                    window.iceShapeChart = new Chart(ctx, {
                        type: 'scatter',
                        plugins: [{
                            beforeDraw: function(chart) {
                                // 确保背景完全透明
                                const ctx = chart.ctx;
                                ctx.save();
                                ctx.globalCompositeOperation = 'destination-over';
                                ctx.fillStyle = 'transparent';
                                ctx.fillRect(0, 0, chart.width, chart.height);
                                ctx.restore();
                            }
                        }],
                        data: {
                            datasets: [
                                {
                                    label: '🛩️ 干净机翼',
                                    data: cleanBody,
                                    borderColor: '#2c3e50',
                                    backgroundColor: 'rgba(44, 62, 80, 0.1)',
                                    borderWidth: 3,
                                    pointRadius: 0,
                                    showLine: true,
                                    fill: false,
                                    tension: 0.1,
                                    hidden: true  // 默认隐藏干净机翼
                                },
                                {
                                    label: '🧊 实验冰形',
                                    data: initialShape,
                                    borderColor: '#3498db',
                                    backgroundColor: 'rgba(52, 152, 219, 0.2)',
                                    borderWidth: 8,  // 更粗的实验冰形线条
                                    pointRadius: 0,
                                    showLine: true,
                                    fill: false,
                                    tension: 0.1,
                                    borderDash: [5, 5]
                                },
                                {
                                    label: '🔮 AI预测冰形',
                                    data: [],
                                    borderColor: '#e74c3c',
                                    backgroundColor: 'rgba(231, 76, 60, 0.3)',
                                    borderWidth: 8,  // 更粗的AI预测冰形线条
                                    pointRadius: 2,
                                    pointBackgroundColor: '#e74c3c',
                                    pointBorderColor: '#ffffff',
                                    pointBorderWidth: 2,
                                    showLine: true,
                                    fill: false,
                                    tension: 0.2,
                                    pointHoverRadius: 6,
                                    pointHoverBackgroundColor: '#c0392b',
                                    pointHoverBorderColor: '#ffffff',
                                    pointHoverBorderWidth: 3
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            devicePixelRatio: window.devicePixelRatio || 1,
                            layout: {
                                padding: 0
                            },
                            elements: {
                                point: {
                                    borderWidth: 0
                                },
                                line: {
                                    borderWidth: 0
                                }
                            },
                            animation: {
                                duration: 1000,
                                easing: 'easeInOutQuart'
                            },
                            plugins: {
                                legend: {
                                    display: true,
                                    position: 'top',
                                    labels: {
                                        usePointStyle: true,
                                        pointStyle: 'line',
                                        font: {
                                            size: 16,
                                            weight: 'bold',
                                            family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                        },
                                        color: '#1f2937',
                                        padding: 18,
                                        boxWidth: 20,
                                        boxHeight: 3
                                    }
                                },
                                tooltip: {
                                    enabled: true,
                                    backgroundColor: 'rgba(17, 24, 39, 0.95)',
                                    titleColor: '#f9fafb',
                                    bodyColor: '#e5e7eb',
                                    borderColor: '#667eea',
                                    borderWidth: 2,
                                    cornerRadius: 10,
                                    displayColors: true,
                                    padding: 12,
                                    titleFont: {
                                        size: 14,
                                        weight: 'bold',
                                        family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                    },
                                    bodyFont: {
                                        size: 13,
                                        family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                    },
                                    callbacks: {
                                        title: function(context) {
                                            return '📍 坐标信息';
                                        },
                                        label: function(context) {
                                            const x = context.parsed.x.toFixed(4);
                                            const y = context.parsed.y.toFixed(4);
                                            return `${context.dataset.label}: (${x}, ${y})`;
                                        }
                                    }
                                },
                                zoom: {
                                    pan: {
                                        enabled: true,
                                        mode: 'xy'
                                    },
                                    zoom: {
                                        wheel: {
                                            enabled: true
                                        },
                                        pinch: {
                                            enabled: true
                                        },
                                        mode: 'xy'
                                    },
                                    limits: {
                                        x: {max: 0.015}  // 限制缩放范围，确保不超过0.015
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    title: {
                                        display: true,
                                        text: '弦向坐标 X (m)',
                                        font: {
                                            size: 16,
                                            weight: 'bold',
                                            family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                        },
                                        color: '#1f2937',
                                        padding: {top: 8, bottom: 4}
                                    },
                                    grid: {
                                        display: false,
                                        drawBorder: false
                                    },
                                    border: {
                                        display: false
                                    },
                                    ticks: {
                                        font: {
                                            size: 14,
                                            family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: 6,
                                        callback: function(value) {
                                            return value.toFixed(3);
                                        }
                                    },
                                    max: 0.015  // 限制x轴显示范围，只显示前缘部分（0.015之前）
                                },
                                y: {
                                    title: {
                                        display: true,
                                        text: '法向坐标 Y (m)',
                                        font: {
                                            size: 16,
                                            weight: 'bold',
                                            family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                        },
                                        color: '#1f2937',
                                        padding: {left: 4, right: 8}
                                    },
                                    grid: {
                                        display: false,
                                        drawBorder: false
                                    },
                                    border: {
                                        display: false
                                    },
                                    ticks: {
                                        font: {
                                            size: 14,
                                            family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: 6,
                                        callback: function(value) {
                                            return value.toFixed(3);
                                        }
                                    }
                                }
                            },
                            interaction: {
                                intersect: false,
                                mode: 'nearest'
                            }
                        }
                    });

                    // 清除之前的动画定时器（如果存在）
                    if (window.iceAnimationInterval) {
                        clearInterval(window.iceAnimationInterval);
                    }
                    if (window.iceResetTimeout) {
                        clearTimeout(window.iceResetTimeout);
                    }

                    // 动态逐点绘制预测冰形（增强版动画）
                    let currentIndex = 0;
                    const interval = 20; // 更快的动画速度
                    let animationPhase = 'drawing'; // 'drawing' 或 'complete'

                    const drawStep = () => {
                        if (animationPhase === 'drawing' && currentIndex < predictedShape.length) {
                            const nextPoint = predictedShape[currentIndex];
                            window.iceShapeChart.data.datasets[2].data.push(nextPoint);
                            
                            // 添加粒子效果（通过更新图表实现）
                            if (currentIndex > 0) {
                                // 为最新添加的点设置特殊样式
                                const lastIndex = window.iceShapeChart.data.datasets[2].data.length - 1;
                                if (lastIndex >= 0) {
                                    // 更新图表并添加闪烁效果
                                    window.iceShapeChart.update('none'); // 无动画更新以保持流畅
                                }
                            } else {
                                window.iceShapeChart.update('none');
                            }
                            
                            currentIndex++;
                            
                            // 进度提示
                            const progress = Math.round((currentIndex / predictedShape.length) * 100);
                            //console.log(`AI预测进度: ${progress}%`);
                            
                        } else if (animationPhase === 'drawing') {
                            // 绘制完成，进入完成阶段
                            animationPhase = 'complete';
                            console.log('🎉 AI预测冰形绘制完成！');
                            
                            // 添加完成动画效果
                            window.iceShapeChart.data.datasets[2].borderWidth = 10;  // 完成时非常粗的线条
                            window.iceShapeChart.data.datasets[2].pointRadius = 3;
                            window.iceShapeChart.update({
                                duration: 800,
                                easing: 'easeInOutElastic'
                            });
                            
                            // 3秒后重置动画
                            window.iceResetTimeout = setTimeout(() => {
                                animationPhase = 'drawing';
                                currentIndex = 0;
                                window.iceShapeChart.data.datasets[2].data = [];
                                window.iceShapeChart.data.datasets[2].borderWidth = 8;  // 重置时保持更粗线条
                                window.iceShapeChart.data.datasets[2].pointRadius = 2;
                                window.iceShapeChart.update({
                                    duration: 500,
                                    easing: 'easeInOutQuart'
                                });
                                console.log('🔄 重新开始AI预测动画...');
                            }, 3500);
                        }
                    };

                    // 保存动画定时器的引用，以便后续清除
                    window.iceAnimationInterval = setInterval(drawStep, interval);

                    // 渲染3D冰形预测结果
                    setTimeout(() => {
                        console.log('开始渲染3D冰形（AI预测）...');
                        show3DIceLoading('正在生成3D冰形...', 20);
                        
                        setTimeout(() => {
                            update3DIceProgress('构建3D网格...', 60);
                            render3DIcePrediction(data, true); // 显示加载状态
                        }, 300);
                    }, 500);

                    // 启用查看详细结果按钮
                    const detailButton = document.querySelector("button[onclick=\"location.href='/details/icing_shape'\"]");
                    if (detailButton) {
                        detailButton.disabled = false;
                        detailButton.style.opacity = '1';
                        detailButton.style.cursor = 'pointer';
                    }

                    // 显示误差分析
                    showErrorAnalysis(data);
                } else {
                    console.error('预测失败:', data.error);
                }
            })
            .catch((error) => {
                console.error('预测失败:', error);
            });
            
            // 同时调用气动系数预测
            // 显示气动系数加载动画
            showAeroChartLoading('lift', '正在预测升力系数');
            showAeroChartLoading('drag', '正在预测阻力系数');

            fetch('/predict_aero_coefficients', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    Temperature: parseFloat(temperature),  // 开尔文温度
                    LWC: parseFloat(lwc),
                    MVD: parseFloat(mvd)
                })
            })
            .then(response => response.json())
            .then((aeroData) => {
                if (aeroData.success) {
                    // 保存气动系数结果
                    saveAeroResults(aeroData);
                    displayAeroResults(aeroData);
                    console.log('✅ 气动系数预测成功');
                } else {
                    console.error('气动系数预测失败:', aeroData.error);
                    // 预测失败时隐藏加载动画
                    hideAeroChartLoading('lift');
                    hideAeroChartLoading('drag');
                }
            })
            .catch((error) => {
                console.error('气动系数预测失败:', error);
                // 预测失败时隐藏加载动画
                hideAeroChartLoading('lift');
                hideAeroChartLoading('drag');
            });

            // 同时调用HTC和Beta预测
            // 显示HTC和Beta加载动画
            showHTCBetaChartLoading('water', '正在预测水滴收集系数');
            showHTCBetaChartLoading('heat', '正在预测对流换热系数');

            fetch('/predict_htc_beta', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    Velocity: parseFloat(velocity),
                    Temperature: parseFloat(temperature),  // 开尔文温度
                    LWC: parseFloat(lwc),
                    MVD: parseFloat(mvd)
                })
            })
            .then(response => response.json())
            .then((htcBetaData) => {
                if (htcBetaData.success) {
                    // 保存HTC和Beta结果
                    saveHTCBetaResults(htcBetaData);
                    displayHTCBetaResults(htcBetaData);
                    console.log('✅ HTC和Beta预测成功');
                } else {
                    console.error('HTC和Beta预测失败:', htcBetaData.error);
                    // 预测失败时隐藏加载动画
                    hideHTCBetaChartLoading('water');
                    hideHTCBetaChartLoading('heat');
                }
            })
            .catch((error) => {
                console.error('HTC和Beta预测失败:', error);
                // 预测失败时隐藏加载动画
                hideHTCBetaChartLoading('water');
                hideHTCBetaChartLoading('heat');
            });
        }

        // 气动系数显示函数
        function displayAeroResults(aeroData) {
            if (!aeroData || !aeroData.success) {
                console.log('无效的气动系数数据');
                return;
            }

            console.log('显示气动系数结果:', aeroData);

            // 清除之前的动画定时器（如果存在）
            if (window.liftAnimationTimeout) {
                clearTimeout(window.liftAnimationTimeout);
                window.liftAnimationTimeout = null;
            }
            if (window.dragAnimationTimeout) {
                clearTimeout(window.dragAnimationTimeout);
                window.dragAnimationTimeout = null;
            }

            // 显示升力系数
            displayLiftCoefficient(aeroData);

            // 显示阻力系数
            displayDragCoefficient(aeroData);
        }

        // HTC和Beta显示函数
        function displayHTCBetaResults(htcBetaData) {
            if (!htcBetaData || !htcBetaData.success) {
                console.log('无效的HTC和Beta数据');
                return;
            }

            console.log('显示HTC和Beta结果:', htcBetaData);

            // 保存数据供3D可视化使用
            lastHTCBetaData = htcBetaData;

            // 清除之前的动画定时器（如果存在）
            if (window.waterAnimationTimeout) {
                clearTimeout(window.waterAnimationTimeout);
                window.waterAnimationTimeout = null;
            }
            if (window.heatAnimationTimeout) {
                clearTimeout(window.heatAnimationTimeout);
                window.heatAnimationTimeout = null;
            }

            // 显示水滴收集系数（Beta）
            displayWaterCollectionCoefficient(htcBetaData);

            // 显示对流换热系数（HTC）
            displayHeatTransferCoefficient(htcBetaData);
        }

        function displayWaterCollectionCoefficient(htcBetaData) {
            const canvas = document.getElementById('chartWaterCollection');
            if (!canvas) {
                console.error('找不到水滴收集系数图表canvas');
                return;
            }

            const ctx = canvas.getContext('2d');

            // 如果已有图表，先销毁
            if (window.waterChart) {
                window.waterChart.destroy();
            }

            // 准备散点数据 - 每个点包含x,y坐标和Beta值
            const coordinates = htcBetaData.coordinates;
            const betaValues = htcBetaData.beta.values;
            const stats = htcBetaData.beta.stats;

            // 创建散点数据，每个点根据Beta值设置颜色
            const scatterData = coordinates.map((coord, index) => {
                const betaValue = betaValues[index];
                const normalizedValue = (betaValue - stats.min) / (stats.max - stats.min);

                // 根据Beta值生成颜色（绿色系）
                const hue = 120; // 绿色
                const saturation = 70 + normalizedValue * 30; // 70-100%
                const lightness = 30 + normalizedValue * 40; // 30-70%
                const color = `hsl(${hue}, ${saturation}%, ${lightness}%)`;

                return {
                    x: coord[0],
                    y: coord[1],
                    betaValue: betaValue
                };
            });

            // 为每个点生成颜色数组
            const pointColors = scatterData.map((point, index) => {
                const normalizedValue = (point.betaValue - stats.min) / (stats.max - stats.min);
                const hue = 120; // 绿色
                const saturation = 70 + normalizedValue * 30; // 70-100%
                const lightness = 30 + normalizedValue * 40; // 30-70%
                return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
            });

            console.log('水滴收集系数散点数据:', {
                dataCount: scatterData.length,
                sampleData: scatterData.slice(0, 3),
                colorCount: pointColors.length,
                sampleColors: pointColors.slice(0, 3),
                stats: stats
            });

            window.waterChart = new Chart(ctx, {
                type: 'scatter',
                plugins: [{
                    beforeDraw: function(chart) {
                        // 确保背景完全透明
                        const ctx = chart.ctx;
                        ctx.save();
                        ctx.globalCompositeOperation = 'destination-over';
                        ctx.fillStyle = 'transparent';
                        ctx.fillRect(0, 0, chart.width, chart.height);
                        ctx.restore();
                    }
                }],
                data: {
                    datasets: [{
                        label: '💧 水滴收集系数分布',
                        data: scatterData,
                        backgroundColor: pointColors,
                        borderColor: pointColors,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                        pointBorderWidth: 1,
                        pointHoverBorderWidth: 2,
                        showLine: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 1500,
                        easing: 'easeInOutQuart'
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#22c55e',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true,
                            callbacks: {
                                title: function(tooltipItems) {
                                    try {
                                        console.log('Tooltip items:', tooltipItems);
                                        if (!tooltipItems || tooltipItems.length === 0) {
                                            return '翼型坐标';
                                        }

                                        const item = tooltipItems[0];
                                        const point = item.raw;

                                        if (point && typeof point.x !== 'undefined' && typeof point.y !== 'undefined') {
                                            return `翼型坐标: (${point.x.toFixed(4)}, ${point.y.toFixed(4)})`;
                                        }

                                        if (item.parsed) {
                                            return `坐标: (${item.parsed.x.toFixed(4)}, ${item.parsed.y.toFixed(4)})`;
                                        }

                                        return '翼型坐标';
                                    } catch (e) {
                                        console.error('Tooltip title error:', e);
                                        return '翼型坐标';
                                    }
                                },
                                label: function(tooltipItem) {
                                    try {
                                        console.log('Tooltip item:', tooltipItem);

                                        // 尝试从raw数据获取
                                        if (tooltipItem.raw && typeof tooltipItem.raw.betaValue !== 'undefined') {
                                            return `水滴收集系数 Beta: ${tooltipItem.raw.betaValue.toFixed(4)}`;
                                        }

                                        // 从数据数组中获取
                                        const dataIndex = tooltipItem.dataIndex;
                                        console.log('Data index:', dataIndex);

                                        if (dataIndex >= 0 && dataIndex < htcBetaData.beta.values.length) {
                                            const betaValue = htcBetaData.beta.values[dataIndex];
                                            console.log('Beta value from array:', betaValue);
                                            return `水滴收集系数 Beta: ${betaValue.toFixed(4)}`;
                                        }

                                        return '水滴收集系数 Beta: 数据不可用';
                                    } catch (e) {
                                        console.error('Tooltip label error:', e);
                                        return '水滴收集系数 Beta: N/A';
                                    }
                                },
                                afterLabel: function(tooltipItem) {
                                    try {
                                        const stats = htcBetaData.beta.stats;
                                        return [
                                            ``,
                                            `统计信息:`,
                                            `平均值: ${stats.mean.toFixed(4)}`,
                                            `最小值: ${stats.min.toFixed(4)}`,
                                            `最大值: ${stats.max.toFixed(4)}`,
                                            `标准差: ${stats.std.toFixed(4)}`
                                        ];
                                    } catch (e) {
                                        console.error('Tooltip afterLabel error:', e);
                                        return [];
                                    }
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'X坐标 (翼型弦长方向)',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Y坐标 (翼型厚度方向)',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // 隐藏加载动画
            hideHTCBetaChartLoading('water');
            console.log('水滴收集系数图表显示完成');

            // 生成弦长分布图
            generateChordwiseDistribution('water', htcBetaData);

            // 强制重新调整图表尺寸
            setTimeout(() => {
                if (window.waterChart) {
                    window.waterChart.resize();
                }
                if (window.waterChordwiseChart) {
                    window.waterChordwiseChart.resize();
                }
                console.log('水滴收集系数图表尺寸已重新调整');
            }, 500);
        }

        function displayHeatTransferCoefficient(htcBetaData) {
            const canvas = document.getElementById('chartHeatTransfer');
            if (!canvas) {
                console.error('找不到对流换热系数图表canvas');
                return;
            }

            const ctx = canvas.getContext('2d');

            // 如果已有图表，先销毁
            if (window.heatChart) {
                window.heatChart.destroy();
            }

            // 准备散点数据 - 每个点包含x,y坐标和HTC值
            const coordinates = htcBetaData.coordinates;
            const htcValues = htcBetaData.htc.values;
            const stats = htcBetaData.htc.stats;

            // 创建散点数据，每个点根据HTC值设置颜色
            const scatterData = coordinates.map((coord, index) => {
                const htcValue = htcValues[index];

                return {
                    x: coord[0],
                    y: coord[1],
                    htcValue: htcValue
                };
            });

            // 为每个点生成颜色数组
            const pointColors = scatterData.map((point, index) => {
                const normalizedValue = (point.htcValue - stats.min) / (stats.max - stats.min);
                const hue = 20 - normalizedValue * 20; // 20-0度，从橙色到红色
                const saturation = 80 + normalizedValue * 20; // 80-100%
                const lightness = 40 + normalizedValue * 30; // 40-70%
                return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
            });

            console.log('对流换热系数散点数据:', {
                dataCount: scatterData.length,
                sampleData: scatterData.slice(0, 3),
                colorCount: pointColors.length,
                sampleColors: pointColors.slice(0, 3),
                stats: stats
            });

            window.heatChart = new Chart(ctx, {
                type: 'scatter',
                plugins: [{
                    beforeDraw: function(chart) {
                        // 确保背景完全透明
                        const ctx = chart.ctx;
                        ctx.save();
                        ctx.globalCompositeOperation = 'destination-over';
                        ctx.fillStyle = 'transparent';
                        ctx.fillRect(0, 0, chart.width, chart.height);
                        ctx.restore();
                    }
                }],
                data: {
                    datasets: [{
                        label: '🔥 对流换热系数分布',
                        data: scatterData,
                        backgroundColor: pointColors,
                        borderColor: pointColors,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                        pointBorderWidth: 1,
                        pointHoverBorderWidth: 2,
                        showLine: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 1500,
                        easing: 'easeInOutQuart'
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#f97316',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true,
                            callbacks: {
                                title: function(tooltipItems) {
                                    try {
                                        console.log('HTC Tooltip items:', tooltipItems);
                                        if (!tooltipItems || tooltipItems.length === 0) {
                                            return '翼型坐标';
                                        }

                                        const item = tooltipItems[0];
                                        const point = item.raw;

                                        if (point && typeof point.x !== 'undefined' && typeof point.y !== 'undefined') {
                                            return `翼型坐标: (${point.x.toFixed(4)}, ${point.y.toFixed(4)})`;
                                        }

                                        if (item.parsed) {
                                            return `坐标: (${item.parsed.x.toFixed(4)}, ${item.parsed.y.toFixed(4)})`;
                                        }

                                        return '翼型坐标';
                                    } catch (e) {
                                        console.error('HTC Tooltip title error:', e);
                                        return '翼型坐标';
                                    }
                                },
                                label: function(tooltipItem) {
                                    try {
                                        console.log('HTC Tooltip item:', tooltipItem);

                                        // 尝试从raw数据获取
                                        if (tooltipItem.raw && typeof tooltipItem.raw.htcValue !== 'undefined') {
                                            return `对流换热系数 HTC: ${tooltipItem.raw.htcValue.toFixed(2)} W/m²·K`;
                                        }

                                        // 从数据数组中获取
                                        const dataIndex = tooltipItem.dataIndex;
                                        console.log('HTC Data index:', dataIndex);

                                        if (dataIndex >= 0 && dataIndex < htcBetaData.htc.values.length) {
                                            const htcValue = htcBetaData.htc.values[dataIndex];
                                            console.log('HTC value from array:', htcValue);
                                            return `对流换热系数 HTC: ${htcValue.toFixed(2)} W/m²·K`;
                                        }

                                        return '对流换热系数 HTC: 数据不可用';
                                    } catch (e) {
                                        console.error('HTC Tooltip label error:', e);
                                        return '对流换热系数 HTC: N/A';
                                    }
                                },
                                afterLabel: function(tooltipItem) {
                                    try {
                                        const stats = htcBetaData.htc.stats;
                                        return [
                                            ``,
                                            `统计信息:`,
                                            `平均值: ${stats.mean.toFixed(2)} W/m²·K`,
                                            `最小值: ${stats.min.toFixed(2)} W/m²·K`,
                                            `最大值: ${stats.max.toFixed(2)} W/m²·K`,
                                            `标准差: ${stats.std.toFixed(2)} W/m²·K`
                                        ];
                                    } catch (e) {
                                        console.error('HTC Tooltip afterLabel error:', e);
                                        return [];
                                    }
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'X坐标 (翼型弦长方向)',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '对流换热系数 (W/m²·K)',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // 隐藏加载动画
            hideHTCBetaChartLoading('heat');
            console.log('对流换热系数图表显示完成');

            // 生成弦长分布图
            generateChordwiseDistribution('heat', htcBetaData);

            // 强制重新调整图表尺寸
            setTimeout(() => {
                if (window.heatChart) {
                    window.heatChart.resize();
                }
                if (window.heatChordwiseChart) {
                    window.heatChordwiseChart.resize();
                }
                console.log('对流换热系数图表尺寸已重新调整');
            }, 500);

            // 自动启动2D/3D切换动画（延迟2秒让用户先看到2D图表）
            setTimeout(() => {
                console.log('自动启动2D/3D切换动画');
                startAutoSwitch('water');
                startAutoSwitch('heat');
                showNotification('已自动启动2D/3D切换动画，每5秒切换一次', 'info');
            }, 2000);
        }

        // 生成弦长分布图函数 - 支持两种X轴表示
        function generateChordwiseDistribution(type, htcBetaData) {
            const canvasId = type === 'water' ? 'chartWaterChordwise' : 'chartHeatChordwise';
            const canvas = document.getElementById(canvasId);
            if (!canvas) {
                console.error(`找不到${type}弦长分布图canvas`);
                return;
            }

            const ctx = canvas.getContext('2d');

            // 如果已有图表，先销毁
            const chartKey = type === 'water' ? 'waterChordwiseChart' : 'heatChordwiseChart';
            if (window[chartKey]) {
                window[chartKey].destroy();
            }

            const coordinates = htcBetaData.coordinates;
            const values = type === 'water' ? htcBetaData.beta.values : htcBetaData.htc.values;
            const unit = type === 'water' ? htcBetaData.beta.unit : htcBetaData.htc.unit;
            const title = type === 'water' ? '水滴收集系数' : '对流换热系数';
            const color1 = type === 'water' ? '#3b82f6' : '#ef4444';
            const color2 = type === 'water' ? '#10b981' : '#f59e0b';

            // 分离上表面（Y>0）和下表面（Y<0）的数据
            const upperSurfaceData = []; // Y>0的数据，映射到X轴负半部分
            const lowerSurfaceData = []; // Y<0的数据，映射到X轴正半部分

            coordinates.forEach((coord, index) => {
                const xCoord = coord[0];
                const yCoord = coord[1];
                const coeffValue = values[index];

                if (yCoord > 0) {
                    // 上表面：X坐标取负值，表示在左半部分
                    upperSurfaceData.push({
                        x: -Math.abs(xCoord), // 映射到负X轴
                        y: coeffValue,
                        originalX: xCoord,
                        originalY: yCoord
                    });
                } else if (yCoord < 0) {
                    // 下表面：X坐标取正值，表示在右半部分
                    lowerSurfaceData.push({
                        x: Math.abs(xCoord), // 映射到正X轴
                        y: coeffValue,
                        originalX: xCoord,
                        originalY: yCoord
                    });
                }
            });

            // 按X坐标排序
            upperSurfaceData.sort((a, b) => a.x - b.x);
            lowerSurfaceData.sort((a, b) => a.x - b.x);

            // 计算统计信息
            const upperStats = {
                min: upperSurfaceData.length > 0 ? Math.min(...upperSurfaceData.map(d => d.y)) : 0,
                max: upperSurfaceData.length > 0 ? Math.max(...upperSurfaceData.map(d => d.y)) : 0,
                avg: upperSurfaceData.length > 0 ? upperSurfaceData.reduce((sum, d) => sum + d.y, 0) / upperSurfaceData.length : 0
            };
            const lowerStats = {
                min: lowerSurfaceData.length > 0 ? Math.min(...lowerSurfaceData.map(d => d.y)) : 0,
                max: lowerSurfaceData.length > 0 ? Math.max(...lowerSurfaceData.map(d => d.y)) : 0,
                avg: lowerSurfaceData.length > 0 ? lowerSurfaceData.reduce((sum, d) => sum + d.y, 0) / lowerSurfaceData.length : 0
            };

            // 创建图表
            window[chartKey] = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: `上表面 (Y>0) ${title} (${unit}) [${upperSurfaceData.length}个点]`,
                        data: upperSurfaceData,
                        borderColor: color1,
                        backgroundColor: color1 + '20',
                        borderWidth: 3,
                        pointRadius: 4,
                        pointHoverRadius: 7,
                        fill: false,
                        tension: 0.2,
                        yAxisID: 'y',
                        pointBackgroundColor: color1,
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2
                    }, {
                        label: `下表面 (Y<0) ${title} (${unit}) [${lowerSurfaceData.length}个点]`,
                        data: lowerSurfaceData,
                        borderColor: color2,
                        backgroundColor: color2 + '20',
                        borderWidth: 3,
                        pointRadius: 4,
                        pointHoverRadius: 7,
                        fill: false,
                        tension: 0.2,
                        yAxisID: 'y',
                        pointBackgroundColor: color2,
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${title}翼型表面分布 [上表面平均: ${upperStats.avg.toFixed(3)}, 下表面平均: ${lowerStats.avg.toFixed(3)}]`,
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            mode: 'point',
                            intersect: false,
                            callbacks: {
                                title: function(context) {
                                    const point = context[0];
                                    const isUpper = point.datasetIndex === 0;
                                    const data = isUpper ? upperSurfaceData[point.dataIndex] : lowerSurfaceData[point.dataIndex];
                                    return `${isUpper ? '上表面' : '下表面'} (X=${data.originalX.toFixed(3)}, Y=${data.originalY.toFixed(3)})`;
                                },
                                label: function(context) {
                                    return `${title}: ${context.parsed.y.toFixed(4)} ${unit}`;
                                },
                                afterLabel: function(context) {
                                    const isUpper = context.datasetIndex === 0;
                                    const mappedX = context.parsed.x;
                                    return `映射X坐标: ${mappedX.toFixed(3)} (${isUpper ? '负值表示上表面' : '正值表示下表面'})`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'linear',
                            display: true,
                            position: 'bottom',
                            title: {
                                display: true,
                                text: 'X坐标 (负值=上表面Y>0, 正值=下表面Y<0)',
                                font: {
                                    size: 10,
                                    weight: 'bold'
                                },
                                color: '#333'
                            },
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: color1
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: `${title} (${unit})`,
                                font: {
                                    size: 11,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                display: false
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(4);
                                },
                                maxTicksLimit: 8,
                                font: {
                                    size: 10
                                }
                            },
                            beginAtZero: false
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

            console.log(`${title}坐标分布图生成完成（双X轴）`);



            // 延迟调整图表尺寸
            setTimeout(() => {
                if (window[chartKey]) {
                    window[chartKey].resize();
                    console.log(`${title}坐标分布图尺寸已调整`);
                }
            }, 200);
        }

        // 全局图表尺寸调整函数
        function resizeAllCharts() {
            const charts = [
                'waterChart', 'heatChart',
                'waterChordwiseChart', 'heatChordwiseChart',
                'liftChart', 'dragChart', 'momentChart'
            ];

            charts.forEach(chartName => {
                if (window[chartName]) {
                    try {
                        window[chartName].resize();
                        console.log(`${chartName} 尺寸已调整`);
                    } catch (error) {
                        console.warn(`调整 ${chartName} 尺寸时出错:`, error);
                    }
                }
            });
        }

        // 监听窗口大小变化
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                resizeAllCharts();
            }, 300);
        });

        // 自动切换2D/3D视图的动画控制
        let autoSwitchIntervals = {
            water: null,
            heat: null
        };
        let autoSwitchEnabled = {
            water: false,
            heat: false
        };

        // 开始自动切换动画
        function startAutoSwitch(type) {
            if (autoSwitchIntervals[type]) {
                clearInterval(autoSwitchIntervals[type]);
            }

            autoSwitchEnabled[type] = true;
            console.log(`开始${type}的自动2D/3D切换动画`);

            // 更新按钮状态
            updateAutoSwitchButton(type, true);

            autoSwitchIntervals[type] = setInterval(() => {
                if (autoSwitchEnabled[type] && lastHTCBetaData) {
                    toggle3DView(type);
                    console.log(`自动切换${type}视图`);
                }
            }, 5000); // 每5秒切换一次
        }

        // 停止自动切换动画
        function stopAutoSwitch(type) {
            if (autoSwitchIntervals[type]) {
                clearInterval(autoSwitchIntervals[type]);
                autoSwitchIntervals[type] = null;
            }
            autoSwitchEnabled[type] = false;
            console.log(`停止${type}的自动2D/3D切换动画`);

            // 更新按钮状态
            updateAutoSwitchButton(type, false);
        }

        // 切换自动动画状态
        function toggleAutoSwitch(type) {
            if (autoSwitchEnabled[type]) {
                stopAutoSwitch(type);
                showNotification(`${type === 'water' ? '水滴收集系数' : '对流换热系数'}自动切换已停止`, 'info');
            } else {
                if (lastHTCBetaData) {
                    startAutoSwitch(type);
                    showNotification(`${type === 'water' ? '水滴收集系数' : '对流换热系数'}自动切换已开始`, 'success');
                } else {
                    showNotification('请先进行预测以获取数据', 'warning');
                }
            }
        }

        // 清理动画类的辅助函数
        function cleanupAnimationClasses(canvas, container3D) {
            // 清理所有动画相关的类
            const animationClasses = ['view-transition', 'view-fade-out', 'view-fade-in'];

            animationClasses.forEach(className => {
                if (canvas) canvas.classList.remove(className);
                if (container3D) container3D.classList.remove(className);
            });

            console.log('🧹 清理动画类完成');
        }

        // 动画辅助函数：切换到3D视图
        function switchTo3DWithAnimation(type, canvas, container3D) {
            console.log(`🎬 开始切换到${type}的3D视图`);

            // 清理之前的动画类
            cleanupAnimationClasses(canvas, container3D);

            // 添加切换效果类
            const chartContent = canvas.closest('.chart-content');
            if (chartContent) {
                chartContent.classList.add('switching-view');
            }

            // 使用requestAnimationFrame确保DOM更新
            requestAnimationFrame(() => {
                // 添加过渡类
                canvas.classList.add('view-transition');
                container3D.classList.add('view-transition');

                // 开始淡出动画
                requestAnimationFrame(() => {
                    canvas.classList.add('view-fade-out');
                });

                setTimeout(() => {
                    // 完全重置3D状态
                    reset3DState(type);
                    // 隐藏2D，显示3D
                    canvas.style.display = 'none';
                    container3D.style.display = 'block';

                    // 初始化3D视图
                    if (lastHTCBetaData) {
                        console.log(`✅ 初始化${type === 'water' ? '水滴收集系数' : '对流换热系数'}3D视图`);
                        if (type === 'water') {
                            init3DWaterVisualization();
                        } else {
                            init3DHeatVisualization();
                        }

                        // 3D容器淡入
                        requestAnimationFrame(() => {
                            container3D.classList.remove('view-fade-out');
                            container3D.classList.add('view-fade-in');
                        });

                        // 清理动画类
                        setTimeout(() => {
                            cleanupAnimationClasses(canvas, container3D);
                            if (chartContent) {
                                chartContent.classList.remove('switching-view');
                            }
                            console.log(`🎬 ${type}切换到3D完成`);
                        }, 600);
                    } else {
                        showNotification('请先进行预测以获取数据', 'warning');
                        // 回滚状态
                        if (type === 'water') {
                            water3DViewActive = false;
                        } else {
                            heat3DViewActive = false;
                        }
                        canvas.style.display = 'block';
                        container3D.style.display = 'none';
                        cleanupAnimationClasses(canvas, container3D);
                        if (chartContent) {
                            chartContent.classList.remove('switching-view');
                        }
                    }
                }, 300); // 等待淡出动画完成
            });
        }

        // 动画辅助函数：切换到2D视图
        function switchTo2DWithAnimation(type, canvas, container3D) {
            console.log(`🎬 开始切换到${type}的2D视图`);

            // 清理之前的动画类
            cleanupAnimationClasses(canvas, container3D);

            // 添加切换效果类
            const chartContent = canvas.closest('.chart-content');
            if (chartContent) {
                chartContent.classList.add('switching-view');
            }

            // 使用requestAnimationFrame确保DOM更新
            requestAnimationFrame(() => {
                // 添加过渡类
                canvas.classList.add('view-transition');
                container3D.classList.add('view-transition');

                // 开始淡出3D
                requestAnimationFrame(() => {
                    container3D.classList.add('view-fade-out');
                });

                setTimeout(() => {
                    console.log(`✅ 切换回${type === 'water' ? '水滴收集系数' : '对流换热系数'}2D视图`);
                    // 显示2D，隐藏3D
                    canvas.style.display = 'block';
                    container3D.style.display = 'none';

                    // 完全重置3D状态
                    reset3DState(type);

                    // 2D画布淡入
                    requestAnimationFrame(() => {
                        canvas.classList.remove('view-fade-out');
                        canvas.classList.add('view-fade-in');
                    });

                    // 强制重新调整图表尺寸和清理动画类
                    setTimeout(() => {
                        resizeAllCharts();
                        cleanupAnimationClasses(canvas, container3D);
                        if (chartContent) {
                            chartContent.classList.remove('switching-view');
                        }
                        console.log(`🎬 ${type}切换到2D完成`);
                    }, 300);
                }, 300); // 等待淡出动画完成
            });
        }

        // 更新自动切换按钮状态
        function updateAutoSwitchButton(type, isActive) {
            const btnId = type === 'water' ? 'waterAutoSwitchBtn' : 'heatAutoSwitchBtn';
            const btn = document.getElementById(btnId);
            if (btn) {
                if (isActive) {
                    btn.style.background = 'linear-gradient(145deg, #10b981, #059669)';
                    btn.style.color = 'white';
                    btn.style.boxShadow = '0 2px 8px rgba(16, 185, 129, 0.3)';
                    btn.title = '停止自动2D/3D切换动画 (每5秒切换)';
                    btn.innerHTML = '⏸️';
                    btn.classList.add('active');
                } else {
                    btn.style.background = '';
                    btn.style.color = '';
                    btn.style.boxShadow = '';
                    btn.title = '开始自动2D/3D切换动画';
                    btn.innerHTML = '🎬';
                    btn.classList.remove('active');
                }
            }
        }

        // 页面离开时清理所有自动切换
        window.addEventListener('beforeunload', () => {
            stopAutoSwitch('water');
            stopAutoSwitch('heat');
        });

        // 页面隐藏时暂停自动切换
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // 页面隐藏时暂停
                if (autoSwitchEnabled.water) {
                    stopAutoSwitch('water');
                    window.waterAutoSwitchWasPaused = true;
                }
                if (autoSwitchEnabled.heat) {
                    stopAutoSwitch('heat');
                    window.heatAutoSwitchWasPaused = true;
                }
            } else {
                // 页面显示时恢复
                if (window.waterAutoSwitchWasPaused && lastHTCBetaData) {
                    startAutoSwitch('water');
                    window.waterAutoSwitchWasPaused = false;
                }
                if (window.heatAutoSwitchWasPaused && lastHTCBetaData) {
                    startAutoSwitch('heat');
                    window.heatAutoSwitchWasPaused = false;
                }
            }
        });

        function displayLiftCoefficient(aeroData) {
            const canvas = document.getElementById('chartLiftCoefficient');
            if (!canvas) {
                console.error('找不到升力系数图表canvas');
                return;
            }

            const ctx = canvas.getContext('2d');

            // 如果已有图表，先销毁
            if (window.liftChart) {
                window.liftChart.destroy();
            }

            // 创建更精美的渐变色
            const cleanGradient = ctx.createLinearGradient(0, 0, 0, 400);
            cleanGradient.addColorStop(0, 'rgba(59, 130, 246, 0.3)');
            cleanGradient.addColorStop(0.5, 'rgba(59, 130, 246, 0.15)');
            cleanGradient.addColorStop(1, 'rgba(59, 130, 246, 0.05)');

            const icedGradient = ctx.createLinearGradient(0, 0, 0, 400);
            icedGradient.addColorStop(0, 'rgba(239, 68, 68, 0.3)');
            icedGradient.addColorStop(0.5, 'rgba(239, 68, 68, 0.15)');
            icedGradient.addColorStop(1, 'rgba(239, 68, 68, 0.05)');

            // 先显示干净构型
            window.liftChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: aeroData.aoa,
                    datasets: [
                        {
                            label: '🔵 干净构型',
                            data: aeroData.clean_lift_coefficient || [],
                            borderColor: '#3b82f6',
                            backgroundColor: cleanGradient,
                            borderWidth: 3,
                            pointRadius: 4,
                            pointHoverRadius: 8,
                            pointBackgroundColor: '#3b82f6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointHoverBackgroundColor: '#ffffff',
                            pointHoverBorderColor: '#3b82f6',
                            pointHoverBorderWidth: 3,
                            pointStyle: 'circle',
                            tension: 0.4,
                            fill: true,
                            cubicInterpolationMode: 'monotone',
                            borderDash: [],
                            borderCapStyle: 'round',
                            borderJoinStyle: 'round'
                        },
                        {
                            label: '❄️ AI预测结冰构型',
                            data: [], // 开始时为空，通过动画添加
                            borderColor: '#ef4444',
                            backgroundColor: icedGradient,
                            borderWidth: 3,
                            pointRadius: 4,
                            pointHoverRadius: 8,
                            pointBackgroundColor: '#ef4444',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointHoverBackgroundColor: '#ffffff',
                            pointHoverBorderColor: '#ef4444',
                            pointHoverBorderWidth: 3,
                            pointStyle: 'rectRot',
                            tension: 0.4,
                            fill: true,
                            cubicInterpolationMode: 'monotone',
                            borderDash: [5, 5],
                            borderCapStyle: 'round',
                            borderJoinStyle: 'round'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    resizeDelay: 0,
                    animation: {
                        duration: 2000,
                        easing: 'easeInOutCubic'
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    hover: {
                        animationDuration: 300
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '攻角 α (°)',
                                font: {
                                    size: 16,
                                    weight: 'bold',
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#1f2937',
                                padding: {top: 10, bottom: 5}
                            },
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#4b5563',
                                font: {
                                    size: 12,
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                padding: 8
                            },
                            border: {
                                display: false
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '升力系数 CL',
                                font: {
                                    size: 16,
                                    weight: 'bold',
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#1f2937',
                                padding: {left: 5, right: 10}
                            },
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#4b5563',
                                font: {
                                    size: 12,
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                padding: 8,
                                callback: function(value) {
                                    return value.toFixed(2);
                                }
                            },
                            border: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: false
                        },
                        legend: {
                            display: true,
                            position: 'top',
                            align: 'center',
                            labels: {
                                font: {
                                    size: 13,
                                    weight: '600',
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#374151',
                                padding: 12,
                                usePointStyle: true,
                                pointStyle: 'circle',
                                boxWidth: 12,
                                boxHeight: 12
                            }
                        },
                        tooltip: {
                            enabled: true,
                            backgroundColor: 'rgba(17, 24, 39, 0.95)',
                            titleColor: '#f9fafb',
                            bodyColor: '#e5e7eb',
                            borderColor: '#3b82f6',
                            borderWidth: 2,
                            cornerRadius: 8,
                            padding: 12,
                            displayColors: true,
                            titleFont: {
                                size: 13,
                                weight: 'bold',
                                family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                            },
                            bodyFont: {
                                size: 12,
                                family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                            },
                            footerFont: {
                                size: 11,
                                family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                            },
                            caretSize: 6,
                            caretPadding: 8,
                            callbacks: {
                                title: function(context) {
                                    return `攻角: ${context[0].label}°`;
                                },
                                label: function(context) {
                                    const value = context.parsed.y.toFixed(4);
                                    const label = context.dataset.label;
                                    if (label.includes('干净')) {
                                        return `🔵 CL = ${value} (基准构型)`;
                                    } else {
                                        return `❄️ CL = ${value} (结冰影响)`;
                                    }
                                },
                                afterLabel: function(context) {
                                    const clean = context.chart.data.datasets[0].data[context.dataIndex];
                                    const iced = context.parsed.y;
                                    if (context.datasetIndex === 1 && clean) {
                                        const change = ((iced - clean) / clean * 100).toFixed(2);
                                        const changeText = change > 0 ? `+${change}%` : `${change}%`;
                                        const impact = Math.abs(change) > 10 ? '显著影响' : Math.abs(change) > 5 ? '中等影响' : '轻微影响';
                                        return [`变化: ${changeText}`, `影响程度: ${impact}`];
                                    }
                                    return null;
                                },
                                footer: function(context) {
                                    if (context[0].datasetIndex === 1) {
                                        return '💡 结冰通常会降低升力系数';
                                    }
                                    return null;
                                }
                            }
                        }
                    }
                }
            });
            
            // 启动AI预测数据的动画
            setTimeout(() => {
                animateLiftCoefficient(aeroData.lift_coefficient);
                // 图表创建完成，隐藏加载动画
                hideAeroChartLoading('lift');
            }, 1000);
        }
        
        function animateLiftCoefficient(fullData) {
            if (!window.liftChart || !fullData) return;

            // 清除之前的动画定时器
            if (window.liftAnimationTimeout) {
                clearTimeout(window.liftAnimationTimeout);
            }

            let currentIndex = 0;
            const animationSpeed = 80; // 优化动画速度：每80ms添加一个点

            const addDataPoint = () => {
                if (currentIndex < fullData.length) {
                    // 逐步添加数据点
                    window.liftChart.data.datasets[1].data.push(fullData[currentIndex]);

                    // 添加闪烁效果到新添加的点
                    const pointCount = window.liftChart.data.datasets[1].data.length;
                    if (pointCount > 0) {
                        // 临时增大最新点的半径
                        const originalRadius = window.liftChart.data.datasets[1].pointRadius;
                        window.liftChart.data.datasets[1].pointRadius = Array(pointCount).fill(4);
                        window.liftChart.data.datasets[1].pointRadius[pointCount - 1] = 8;

                        // 使用更平滑的动画更新
                        window.liftChart.update({
                            duration: 200,
                            easing: 'easeOutCubic'
                        });

                        // 恢复正常半径
                        setTimeout(() => {
                            if (window.liftChart && window.liftChart.data.datasets[1]) {
                                window.liftChart.data.datasets[1].pointRadius = 4;
                                window.liftChart.update('none');
                            }
                        }, 200);
                    }

                    currentIndex++;
                    window.liftAnimationTimeout = setTimeout(() => {
                        requestAnimationFrame(addDataPoint);
                    }, animationSpeed);
                } else {
                    // 绘制完成，添加完成效果
                    if (window.liftChart && window.liftChart.data.datasets[1]) {
                        // 短暂高亮整条曲线
                        const originalBorderWidth = window.liftChart.data.datasets[1].borderWidth;
                        window.liftChart.data.datasets[1].borderWidth = 4;
                        window.liftChart.update({
                            duration: 300,
                            easing: 'easeOutCubic'
                        });

                        setTimeout(() => {
                            if (window.liftChart && window.liftChart.data.datasets[1]) {
                                window.liftChart.data.datasets[1].borderWidth = originalBorderWidth;
                                window.liftChart.update('none');
                            }
                        }, 500);
                    }

                    // 停留5秒后重新开始循环
                    window.liftAnimationTimeout = setTimeout(() => {
                        // 清空AI预测数据，重新开始
                        if (window.liftChart && window.liftChart.data.datasets[1]) {
                            window.liftChart.data.datasets[1].data = [];
                            window.liftChart.update({
                                duration: 400,
                                easing: 'easeInCubic'
                            });
                            window.liftAnimationTimeout = setTimeout(() => {
                                animateLiftCoefficient(fullData);
                            }, 500);
                        }
                    }, 5000);
                }
            };

            addDataPoint();
        }
        
        function displayDragCoefficient(aeroData) {
            const canvas = document.getElementById('chartDragCoefficient');
            if (!canvas) {
                console.error('找不到阻力系数图表canvas');
                return;
            }

            const ctx = canvas.getContext('2d');

            // 如果已有图表，先销毁
            if (window.dragChart) {
                window.dragChart.destroy();
            }

            // 创建更精美的渐变色
            const cleanGradient = ctx.createLinearGradient(0, 0, 0, 400);
            cleanGradient.addColorStop(0, 'rgba(16, 185, 129, 0.3)');
            cleanGradient.addColorStop(0.5, 'rgba(16, 185, 129, 0.15)');
            cleanGradient.addColorStop(1, 'rgba(16, 185, 129, 0.05)');

            const icedGradient = ctx.createLinearGradient(0, 0, 0, 400);
            icedGradient.addColorStop(0, 'rgba(245, 158, 11, 0.3)');
            icedGradient.addColorStop(0.5, 'rgba(245, 158, 11, 0.15)');
            icedGradient.addColorStop(1, 'rgba(245, 158, 11, 0.05)');

            // 先显示干净构型
            window.dragChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: aeroData.aoa,
                    datasets: [
                        {
                            label: '🔵 干净构型',
                            data: aeroData.clean_drag_coefficient || [],
                            borderColor: '#10b981',
                            backgroundColor: cleanGradient,
                            borderWidth: 3,
                            pointRadius: 4,
                            pointHoverRadius: 8,
                            pointBackgroundColor: '#10b981',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointHoverBackgroundColor: '#ffffff',
                            pointHoverBorderColor: '#10b981',
                            pointHoverBorderWidth: 3,
                            pointStyle: 'triangle',
                            tension: 0.4,
                            fill: true,
                            cubicInterpolationMode: 'monotone',
                            borderDash: [],
                            borderCapStyle: 'round',
                            borderJoinStyle: 'round'
                        },
                        {
                            label: '❄️ AI预测结冰构型',
                            data: [], // 开始时为空，通过动画添加
                            borderColor: '#f59e0b',
                            backgroundColor: icedGradient,
                            borderWidth: 3,
                            pointRadius: 4,
                            pointHoverRadius: 8,
                            pointBackgroundColor: '#f59e0b',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointHoverBackgroundColor: '#ffffff',
                            pointHoverBorderColor: '#f59e0b',
                            pointHoverBorderWidth: 3,
                            pointStyle: 'star',
                            tension: 0.4,
                            fill: true,
                            cubicInterpolationMode: 'monotone',
                            borderDash: [5, 5],
                            borderCapStyle: 'round',
                            borderJoinStyle: 'round'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    resizeDelay: 0,
                    animation: {
                        duration: 2000,
                        easing: 'easeInOutCubic'
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    hover: {
                        animationDuration: 300
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '攻角 α (°)',
                                font: {
                                    size: 16,
                                    weight: 'bold',
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#1f2937',
                                padding: {top: 10, bottom: 5}
                            },
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#4b5563',
                                font: {
                                    size: 12,
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                padding: 8
                            },
                            border: {
                                display: false
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '阻力系数 CD',
                                font: {
                                    size: 16,
                                    weight: 'bold',
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#1f2937',
                                padding: {left: 5, right: 10}
                            },
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#4b5563',
                                font: {
                                    size: 12,
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                padding: 8,
                                callback: function(value) {
                                    return value.toFixed(4);
                                }
                            },
                            border: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: false
                        },
                        legend: {
                            display: true,
                            position: 'top',
                            align: 'center',
                            labels: {
                                font: {
                                    size: 13,
                                    weight: '600',
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#374151',
                                padding: 12,
                                usePointStyle: true,
                                pointStyle: 'triangle',
                                boxWidth: 12,
                                boxHeight: 12
                            }
                        },
                        tooltip: {
                            enabled: true,
                            backgroundColor: 'rgba(17, 24, 39, 0.95)',
                            titleColor: '#f9fafb',
                            bodyColor: '#e5e7eb',
                            borderColor: '#10b981',
                            borderWidth: 2,
                            cornerRadius: 8,
                            padding: 12,
                            displayColors: true,
                            titleFont: {
                                size: 13,
                                weight: 'bold',
                                family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                            },
                            bodyFont: {
                                size: 12,
                                family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                            },
                            footerFont: {
                                size: 11,
                                family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                            },
                            caretSize: 6,
                            caretPadding: 8,
                            callbacks: {
                                title: function(context) {
                                    return `攻角: ${context[0].label}°`;
                                },
                                label: function(context) {
                                    const value = context.parsed.y.toFixed(5);
                                    const label = context.dataset.label;
                                    if (label.includes('干净')) {
                                        return `🔵 CD = ${value} (基准构型)`;
                                    } else {
                                        return `❄️ CD = ${value} (结冰影响)`;
                                    }
                                },
                                afterLabel: function(context) {
                                    const clean = context.chart.data.datasets[0].data[context.dataIndex];
                                    const iced = context.parsed.y;
                                    if (context.datasetIndex === 1 && clean) {
                                        const change = ((iced - clean) / clean * 100).toFixed(2);
                                        const changeText = change > 0 ? `+${change}%` : `${change}%`;
                                        const impact = Math.abs(change) > 15 ? '显著影响' : Math.abs(change) > 8 ? '中等影响' : '轻微影响';
                                        return [`变化: ${changeText}`, `影响程度: ${impact}`];
                                    }
                                    return null;
                                },
                                footer: function(context) {
                                    if (context[0].datasetIndex === 1) {
                                        return '💡 结冰通常会增加阻力系数';
                                    }
                                    return null;
                                }
                            }
                        }
                    }
                }
            });
            
            // 启动AI预测数据的动画，稍微延迟以避免与升力系数同时开始
            setTimeout(() => {
                animateDragCoefficient(aeroData.drag_coefficient);
                // 图表创建完成，隐藏加载动画
                hideAeroChartLoading('drag');
            }, 1500);
        }
        
        function animateDragCoefficient(fullData) {
            if (!window.dragChart || !fullData) return;

            // 清除之前的动画定时器
            if (window.dragAnimationTimeout) {
                clearTimeout(window.dragAnimationTimeout);
            }

            let currentIndex = 0;
            const animationSpeed = 85; // 优化动画速度：每85ms添加一个点，与升力系数略有差异

            const addDataPoint = () => {
                if (currentIndex < fullData.length) {
                    // 逐步添加数据点
                    window.dragChart.data.datasets[1].data.push(fullData[currentIndex]);

                    // 添加闪烁效果到新添加的点
                    const pointCount = window.dragChart.data.datasets[1].data.length;
                    if (pointCount > 0) {
                        // 临时增大最新点的半径
                        const originalRadius = window.dragChart.data.datasets[1].pointRadius;
                        window.dragChart.data.datasets[1].pointRadius = Array(pointCount).fill(4);
                        window.dragChart.data.datasets[1].pointRadius[pointCount - 1] = 8;

                        // 使用更平滑的动画更新
                        window.dragChart.update({
                            duration: 200,
                            easing: 'easeOutCubic'
                        });

                        // 恢复正常半径
                        setTimeout(() => {
                            if (window.dragChart && window.dragChart.data.datasets[1]) {
                                window.dragChart.data.datasets[1].pointRadius = 4;
                                window.dragChart.update('none');
                            }
                        }, 200);
                    }

                    currentIndex++;
                    window.dragAnimationTimeout = setTimeout(() => {
                        requestAnimationFrame(addDataPoint);
                    }, animationSpeed);
                } else {
                    // 绘制完成，添加完成效果
                    if (window.dragChart && window.dragChart.data.datasets[1]) {
                        // 短暂高亮整条曲线
                        const originalBorderWidth = window.dragChart.data.datasets[1].borderWidth;
                        window.dragChart.data.datasets[1].borderWidth = 4;
                        window.dragChart.update({
                            duration: 300,
                            easing: 'easeOutCubic'
                        });

                        setTimeout(() => {
                            if (window.dragChart && window.dragChart.data.datasets[1]) {
                                window.dragChart.data.datasets[1].borderWidth = originalBorderWidth;
                                window.dragChart.update('none');
                            }
                        }, 500);
                    }

                    // 停留5秒后重新开始循环
                    window.dragAnimationTimeout = setTimeout(() => {
                        // 清空AI预测数据，重新开始
                        if (window.dragChart && window.dragChart.data.datasets[1]) {
                            window.dragChart.data.datasets[1].data = [];
                            window.dragChart.update({
                                duration: 400,
                                easing: 'easeInCubic'
                            });
                            window.dragAnimationTimeout = setTimeout(() => {
                                animateDragCoefficient(fullData);
                            }, 500);
                        }
                    }, 5000);
                }
            };

            addDataPoint();
        }

        function displayIceShapeResults(data) {
            if (!data || !data.success) {
                console.log('无效的预测数据');
                return;
            }
            
            // 安全地获取图表canvas元素
            let chartElement = document.getElementById('chartIcingShape');
            if (!chartElement) {
                console.warn('找不到chartIcingShape元素，尝试创建...');
                
                // 优先在ice2DContainer中创建2D图表
                const ice2DContainer = document.getElementById('ice2DContainer');
                const possibleContainers = [
                    ice2DContainer,
                    document.getElementById('displayModule'),
                    document.querySelector('.chart-container'),
                    document.querySelector('.ice-shape-container'),
                    document.body
                ].filter(Boolean);
                
                if (possibleContainers.length > 0) {
                    chartElement = document.createElement('canvas');
                    chartElement.id = 'chartIcingShape';
                    
                    // 如果是在ice2DContainer中创建，先清空占位内容并获取实际尺寸
                    if (possibleContainers[0] === ice2DContainer) {
                        const placeholder = ice2DContainer.querySelector('.ice-2d-placeholder');
                        if (placeholder) {
                            placeholder.remove();
                        }
                        
                        // 获取ice2DContainer的实际计算尺寸
                        const containerRect = ice2DContainer.getBoundingClientRect();
                        const containerStyle = window.getComputedStyle(ice2DContainer);
                        const borderWidth = parseInt(containerStyle.borderLeftWidth) + parseInt(containerStyle.borderRightWidth);
                        const paddingWidth = parseInt(containerStyle.paddingLeft) + parseInt(containerStyle.paddingRight);
                        const borderHeight = parseInt(containerStyle.borderTopWidth) + parseInt(containerStyle.borderBottomWidth);
                        const paddingHeight = parseInt(containerStyle.paddingTop) + parseInt(containerStyle.paddingBottom);
                        
                        // 计算Canvas的实际可用尺寸
                        let canvasWidth = containerRect.width - borderWidth - paddingWidth;
                        let canvasHeight = containerRect.height - borderHeight - paddingHeight;

                        // 优化长屏幕显示：确保合适的宽高比
                        const aspectRatio = canvasWidth / canvasHeight;
                        const idealAspectRatio = 2.5; // 理想的宽高比，适合翼型显示

                        if (aspectRatio > idealAspectRatio) {
                            // 屏幕太宽，限制宽度保持比例
                            const maxWidth = canvasHeight * idealAspectRatio;
                            canvasWidth = Math.min(canvasWidth, maxWidth);
                        } else if (aspectRatio < 1.8) {
                            // 屏幕太窄，限制高度保持比例
                            const maxHeight = canvasWidth / 1.8;
                            canvasHeight = Math.min(canvasHeight, maxHeight);
                        }

                        // 设置Canvas样式和尺寸，居中显示
                        chartElement.style.width = canvasWidth + 'px';
                        chartElement.style.height = canvasHeight + 'px';
                        chartElement.style.position = 'absolute';
                        chartElement.style.top = '50%';
                        chartElement.style.left = '50%';
                        chartElement.style.transform = 'translate(-50%, -50%)';
                        chartElement.style.borderRadius = '8px';
                        chartElement.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';

                        // 设置Canvas的实际分辨率（devicePixelRatio适配）
                        const devicePixelRatio = window.devicePixelRatio || 1;
                        chartElement.width = canvasWidth * devicePixelRatio;
                        chartElement.height = canvasHeight * devicePixelRatio;

                        // 重要：调整Canvas上下文缩放以确保清晰度
                        const ctx = chartElement.getContext('2d');
                        ctx.scale(devicePixelRatio, devicePixelRatio);

                        ice2DContainer.appendChild(chartElement);
                        console.log('已在ice2DContainer中创建chartIcingShape元素用于恢复数据，尺寸:', canvasWidth + 'x' + canvasHeight, 'DPR:', devicePixelRatio);
                    } else {
                        // 其他容器的处理保持原样
                        chartElement.style.width = '100%';
                        chartElement.style.height = '100%';
                        chartElement.style.position = 'absolute';
                        chartElement.style.top = '0';
                        chartElement.style.left = '0';
                        
                        possibleContainers[0].appendChild(chartElement);
                        console.log('已创建chartIcingShape元素用于恢复数据');
                    }
                } else {
                    console.error('无法找到合适的容器来创建图表元素');
                    return;
                }
            }
            
            const ctx = chartElement.getContext('2d');
            if (!ctx) {
                console.error('无法获取canvas上下文');
                return;
            }
            
            // 如果已有图表，先销毁
            if (window.iceShapeChart) {
                window.iceShapeChart.destroy();
            }

            // 过滤前缘部分数据用于干净机翼显示
            const filterLeadingEdge = (shape, threshold = 0.04) => {
                return shape.filter(point => point[0] <= threshold);
            };

            // 恢复数据时也使用完整轮廓，通过图表显示范围来控制可见区域
            const cleanBody = filterLeadingEdge(data.clean_body).map(point => ({ x: point[0], y: point[1] }));
            const initialShape = data.initial_shape.map(point => ({ x: point[0], y: point[1] })); // 完整实验冰形轮廓
            const predictedShape = data.predicted_shape.map(point => ({ x: point[0], y: point[1] })); // 完整预测冰形轮廓

            // 初始化图表（恢复数据时使用相同的增强样式）
            window.iceShapeChart = new Chart(ctx, {
                type: 'scatter',
                plugins: [{
                    beforeDraw: function(chart) {
                        // 确保背景完全透明
                        const ctx = chart.ctx;
                        ctx.save();
                        ctx.globalCompositeOperation = 'destination-over';
                        ctx.fillStyle = 'transparent';
                        ctx.fillRect(0, 0, chart.width, chart.height);
                        ctx.restore();
                    }
                }],
                data: {
                    datasets: [
                        {
                            label: '🛩️ 干净机翼',
                            data: cleanBody,
                            borderColor: '#2c3e50',
                            backgroundColor: 'rgba(44, 62, 80, 0.1)',
                            borderWidth: 3,
                            pointRadius: 0,
                            showLine: true,
                            fill: false,
                            tension: 0.1,
                            hidden: true  // 默认隐藏干净机翼
                        },
                        {
                            label: '🧊 实验冰形',
                            data: initialShape,
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.2)',
                            borderWidth: 8,  // 更粗的实验冰形线条
                            pointRadius: 0,
                            showLine: true,
                            fill: false,
                            tension: 0.1,
                            borderDash: [5, 5]
                        },
                        {
                            label: '🔮 AI预测冰形',
                            data: [],
                            borderColor: '#e74c3c',
                            backgroundColor: 'rgba(231, 76, 60, 0.3)',
                            borderWidth: 8,  // 恢复数据时保持更粗线条
                            pointRadius: 2,
                            pointBackgroundColor: '#e74c3c',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            showLine: true,
                            fill: false,
                            tension: 0.2,
                            pointHoverRadius: 6,
                            pointHoverBackgroundColor: '#c0392b',
                            pointHoverBorderColor: '#ffffff',
                            pointHoverBorderWidth: 3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    devicePixelRatio: window.devicePixelRatio || 1,
                    layout: {
                        padding: 0
                    },
                    elements: {
                        point: {
                            borderWidth: 0
                        },
                        line: {
                            borderWidth: 0
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                pointStyle: 'line',
                                font: {
                                    size: 16,
                                    weight: 'bold',
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#1f2937',
                                padding: 18,
                                boxWidth: 20,
                                boxHeight: 3
                            }
                        },
                        tooltip: {
                            enabled: true,
                            backgroundColor: 'rgba(17, 24, 39, 0.95)',
                            titleColor: '#f9fafb',
                            bodyColor: '#e5e7eb',
                            borderColor: '#667eea',
                            borderWidth: 2,
                            cornerRadius: 10,
                            displayColors: true,
                            padding: 12,
                            titleFont: {
                                size: 14,
                                weight: 'bold',
                                family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                            },
                            bodyFont: {
                                size: 13,
                                family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                            },
                            callbacks: {
                                title: function(context) {
                                    return '📍 坐标信息';
                                },
                                label: function(context) {
                                    const x = context.parsed.x.toFixed(4);
                                    const y = context.parsed.y.toFixed(4);
                                    return `${context.dataset.label}: (${x}, ${y})`;
                                }
                            }
                        },
                        zoom: {
                            pan: {
                                enabled: true,
                                mode: 'xy'
                            },
                            zoom: {
                                wheel: {
                                    enabled: true
                                },
                                pinch: {
                                    enabled: true
                                },
                                mode: 'xy'
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '弦向坐标 X (m)',
                                font: {
                                    size: 16,
                                    weight: 'bold',
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#1f2937',
                                padding: {top: 8, bottom: 4}
                            },
                            grid: {
                                display: false,
                                drawBorder: false
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 14,
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#374151',
                                padding: 6,
                                callback: function(value) {
                                    return value.toFixed(3);
                                }
                            },
                            max: 0.015  // 限制x轴显示范围，只显示前缘部分（0.015之前）
                        },
                        y: {
                            title: {
                                display: true,
                                text: '法向坐标 Y (m)',
                                font: {
                                    size: 16,
                                    weight: 'bold',
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#1f2937',
                                padding: {left: 4, right: 8}
                            },
                            grid: {
                                display: false,
                                drawBorder: false
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 14,
                                    family: "'Segoe UI', 'Microsoft YaHei', sans-serif"
                                },
                                color: '#374151',
                                padding: 6,
                                callback: function(value) {
                                    return value.toFixed(3);
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'nearest'
                    }
                }
            });

            // 清除之前的动画定时器（如果存在）
            if (window.iceAnimationInterval) {
                clearInterval(window.iceAnimationInterval);
            }
            if (window.iceResetTimeout) {
                clearTimeout(window.iceResetTimeout);
            }

            // 动态逐点绘制预测冰形（增强版动画 - 恢复数据时）
            let currentIndex = 0;
            const interval = 20; // 更快的动画速度
            let animationPhase = 'drawing'; // 'drawing' 或 'complete'

            const drawStep = () => {
                if (animationPhase === 'drawing' && currentIndex < predictedShape.length) {
                    const nextPoint = predictedShape[currentIndex];
                    window.iceShapeChart.data.datasets[2].data.push(nextPoint);
                    
                    // 更新图表并添加效果
                    if (currentIndex > 0) {
                        window.iceShapeChart.update('none'); // 无动画更新以保持流畅
                    } else {
                        window.iceShapeChart.update('none');
                    }
                    
                    currentIndex++;
                    
                    // 进度提示
                    const progress = Math.round((currentIndex / predictedShape.length) * 100);
                    if (progress % 20 === 0) { // 每20%显示一次进度
                        //console.log(`🔄 恢复AI预测进度: ${progress}%`);
                    }
                    
                } else if (animationPhase === 'drawing') {
                    // 绘制完成，进入完成阶段
                    animationPhase = 'complete';
                    //console.log('✨ AI预测冰形恢复完成！');
                    
                    // 添加完成动画效果
                    window.iceShapeChart.data.datasets[2].borderWidth = 10;  // 完成时非常粗的线条
                    window.iceShapeChart.data.datasets[2].pointRadius = 3;
                    window.iceShapeChart.update({
                        duration: 800,
                        easing: 'easeInOutElastic'
                    });
                    
                    // 3.5秒后重置动画
                    window.iceResetTimeout = setTimeout(() => {
                        animationPhase = 'drawing';
                        currentIndex = 0;
                        window.iceShapeChart.data.datasets[2].data = [];
                        window.iceShapeChart.data.datasets[2].borderWidth = 8;  // 重置时保持更粗线条
                        window.iceShapeChart.data.datasets[2].pointRadius = 2;
                        window.iceShapeChart.update({
                            duration: 500,
                            easing: 'easeInOutQuart'
                        });
                        //console.log('🔄 重新开始AI预测展示...');
                    }, 3500);
                }
            };

            // 保存动画定时器的引用，以便后续清除
            window.iceAnimationInterval = setInterval(drawStep, interval);

            // 启用查看详细结果按钮
            const detailButton = document.querySelector("button[onclick=\"location.href='/details/icing_shape'\"]");
            if (detailButton) {
                detailButton.disabled = false;
                detailButton.style.opacity = '1';
                detailButton.style.cursor = 'pointer';
            }
        }

        function createChart(canvasId, label, data) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: [label],
                    datasets: [{
                        label: '预测结果',
                        data: data,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function showModule(moduleId) {
            // 安全地查找模块元素
            const modules = document.querySelectorAll('.module');
            modules.forEach(module => {
                if (module && module.style) {
                    module.style.display = 'none';
                }
            });
            
            const targetModule = document.getElementById(moduleId);
            if (targetModule && targetModule.style) {
                targetModule.style.display = 'block';
                console.log(`成功显示模块: ${moduleId}`);
            } else {
                console.log(`模块 ${moduleId} 未找到，跳过显示操作`);
                
                // 如果是displayModule不存在，尝试查找其他可能的元素
                if (moduleId === 'displayModule') {
                    const allElements = document.querySelectorAll('[id*="display"], [class*="display"], [id*="main"], [class*="main"]');
                    console.log('页面中可能的显示元素:', Array.from(allElements).map(el => el.id || el.className));
                    
                    // 尝试显示第一个找到的相关元素
                    if (allElements.length > 0) {
                        allElements[0].style.display = 'block';
                        console.log('使用备用显示元素:', allElements[0].id || allElements[0].className);
                    }
                }
            }
        }

        function updateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = now.getHours();
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const period = hours >= 12 ? '下午' : '上午';
            const formattedHours = hours % 12 || 12; // 转换为12小时制

            const formattedTime = `${year}年${month}月${day}日 ${period} ${formattedHours}:${minutes}:${seconds}`;
            document.getElementById('current-time').textContent = `北京时间: ${formattedTime}`;
        }

        setInterval(updateTime, 1000); // 每秒更新一次时间
        updateTime(); // 初始化时间显示

        // 初始化3D冰形可视化
        function init3DIceVisualization() {
            const container = document.getElementById('ice3DContainer');
            if (!container) {
                console.error('找不到ice3DContainer容器');
                update3DIceProgress('容器不存在', 0);
                setTimeout(() => hide3DIceLoading(), 2000);
                return;
            }
            
            console.log('开始初始化3D冰形场景...');
            update3DIceProgress('正在检查依赖...', 40);
            
            // 检查Three.js是否可用
            if (typeof THREE === 'undefined') {
                console.error('Three.js未加载，无法初始化3D场景');
                update3DIceProgress('Three.js未加载', 0);
                setTimeout(() => hide3DIceLoading(), 2000);
                return;
            }
            
            update3DIceProgress('正在创建3D场景...', 50);
            
            // 创建Three.js场景
            ice3DScene = new THREE.Scene();
            ice3DScene.background = new THREE.Color(0xffffff);  // 纯白色背景
            
            // 创建相机
            const containerRect = container.getBoundingClientRect();
            ice3DCamera = new THREE.PerspectiveCamera(
                75, 
                containerRect.width / containerRect.height, 
                0.1, 
                1000
            );
            ice3DCamera.position.set(-1.84, 1.31, -0.62);  // 恢复用户确认的最佳角度
            
            update3DIceProgress('正在初始化渲染器...', 70);
            
            // 创建渲染器
            ice3DRenderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            ice3DRenderer.setSize(containerRect.width, containerRect.height);
            ice3DRenderer.shadowMap.enabled = true;
            ice3DRenderer.shadowMap.type = THREE.PCFSoftShadowMap;
            
            // 将渲染器添加到正确的容器
            const canvasContainer = document.getElementById('ice3DCanvas');
            if (canvasContainer) {
                // 清空容器
                canvasContainer.innerHTML = '';
                canvasContainer.appendChild(ice3DRenderer.domElement);
                console.log('3D渲染器canvas已添加到ice3DCanvas容器');
            } else {
                // 直接添加到主容器
                container.appendChild(ice3DRenderer.domElement);
                console.log('3D渲染器canvas添加到主容器');
            }
            
            update3DIceProgress('正在设置控制器...', 80);
            
            // 检查OrbitControls是否可用
            if (typeof THREE.OrbitControls !== 'undefined') {
                // 添加轨道控制器
                ice3DControls = new THREE.OrbitControls(ice3DCamera, ice3DRenderer.domElement);
                ice3DControls.enableDamping = true;
                ice3DControls.dampingFactor = 0.05;
                ice3DControls.target.set(0, 0, 0);
                
                // 添加相机位置记录功能
                ice3DControls.addEventListener('change', function() {
                    // 将相机位置暴露到全局，方便控制台访问
                    window.currentCameraPosition = {
                        position: {
                            x: ice3DCamera.position.x,
                            y: ice3DCamera.position.y,
                            z: ice3DCamera.position.z
                        },
                        target: {
                            x: ice3DControls.target.x,
                            y: ice3DControls.target.y,
                            z: ice3DControls.target.z
                        }
                    };
                });
                
                console.log('轨道控制器已添加');
                console.log('💡 提示：在控制台输入 logCameraPosition() 可以记录当前相机位置');
                console.log('💡 提示：在控制台输入 window.currentCameraPosition 可以查看当前相机数据');
            } else {
                console.warn('OrbitControls不可用');
            }
            
            // 添加全局函数用于记录相机位置
            window.logCameraPosition = function() {
                const pos = ice3DCamera.position;
                const target = ice3DControls ? ice3DControls.target : { x: 0, y: 0, z: 0 };
                
                console.log('===== 当前3D相机位置 =====');
                console.log('相机位置:', `ice3DCamera.position.set(${pos.x.toFixed(2)}, ${pos.y.toFixed(2)}, ${pos.z.toFixed(2)});`);
                console.log('目标位置:', `ice3DControls.target.set(${target.x.toFixed(2)}, ${target.y.toFixed(2)}, ${target.z.toFixed(2)});`);
                console.log('完整代码:');
                console.log(`ice3DCamera.position.set(${pos.x.toFixed(2)}, ${pos.y.toFixed(2)}, ${pos.z.toFixed(2)});`);
                console.log(`if (ice3DControls) {`);
                console.log(`    ice3DControls.target.set(${target.x.toFixed(2)}, ${target.y.toFixed(2)}, ${target.z.toFixed(2)});`);
                console.log(`    ice3DControls.update();`);
                console.log(`}`);
                console.log('========================');
                
                return {
                    position: { x: pos.x, y: pos.y, z: pos.z },
                    target: { x: target.x, y: target.y, z: target.z }
                };
            };
            
            // 添加环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
            ice3DScene.add(ambientLight);
            
            // 添加方向光
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
            directionalLight.position.set(5, 5, 5);
            directionalLight.castShadow = true;
            ice3DScene.add(directionalLight);
            
            // 添加点光源
            const pointLight = new THREE.PointLight(0x64ffda, 0.3, 100);
            pointLight.position.set(-5, 5, 5);
            ice3DScene.add(pointLight);
            
            update3DIceProgress('正在设置界面元素...', 90);
            
            // 不添加坐标轴辅助器，保持界面简洁
            // const axesHelper = new THREE.AxesHelper(2);
            // ice3DScene.add(axesHelper);
            
            // 不添加任何测试数据，保持场景完全干净，等待用户的真实预测数据
            console.log('3D场景已准备就绪，完全不使用任何模拟数据');
            
            // 显示图例（控制面板已集成在图例中）
            const legendElement = document.getElementById('ice3DLegend');
            
            if (legendElement) {
                legendElement.style.display = 'block';
                console.log('显示了图例（含集成的控制面板）');
            }
            
            console.log('3D场景初始化完成');
            
            // 检查是否有现有的预测数据来决定是否隐藏加载框
            const savedPrediction = localStorage.getItem('iceShapePrediction');
            if (!savedPrediction) {
                // 没有预测数据，完成初始化后隐藏加载框
                update3DIceProgress('3D场景初始化完成', 100);
                // 在window.onload中会处理隐藏加载框的逻辑
            }
            
            // 不自动显示示例3D冰形，只在没有真实数据时才显示
            // setTimeout(() => {
            //     showSample3DIceShape();
            // }, 1000);
            
            // 启动渲染循环
            function animate() {
                ice3DAnimationId = requestAnimationFrame(animate);
                
                // 自动上下移动功能：让相机在Y轴上做往复运动
                if (ice3DAutoMove && ice3DCamera) {
                    // 计算新的Y位置
                    const currentY = ice3DCamera.position.y;
                    const targetMaxY = ice3DBaseCameraY + ice3DMoveRange;
                    const targetMinY = ice3DBaseCameraY - ice3DMoveRange;
                    
                    // 计算下一帧的Y位置
                    let newY = currentY + (ice3DMoveSpeed * ice3DMoveDirection);
                    
                    // 检查边界并反转方向
                    if (newY >= targetMaxY) {
                        newY = targetMaxY;
                        ice3DMoveDirection = -1; // 向下
                    } else if (newY <= targetMinY) {
                        newY = targetMinY;
                        ice3DMoveDirection = 1; // 向上
                    }
                    
                    // 更新相机Y位置，保持X和Z不变
                    ice3DCamera.position.y = newY;
                }
                
                if (ice3DControls) {
                    ice3DControls.update();
                }
                ice3DRenderer.render(ice3DScene, ice3DCamera);
            }
            animate();
            console.log('渲染循环已启动，场景完全干净等待用户数据');
            
            // 自动上下移动控制函数
            window.toggleAutoMove = function() {
                ice3DAutoMove = !ice3DAutoMove;
                const toggleButton = document.getElementById('ice3DMoveToggle');
                if (toggleButton) {
                    toggleButton.textContent = ice3DAutoMove ? '暂停' : '启动';
                    toggleButton.style.background = ice3DAutoMove ? '#007bff' : '#28a745';
                }
                console.log('3D自动上下移动:', ice3DAutoMove ? '启动' : '暂停');
            };
            
            // 用户交互时暂停自动移动的逻辑
            if (ice3DControls) {
                let userInteractionTimeout = null;
                
                // 监听控制器开始事件
                ice3DControls.addEventListener('start', function() {
                    ice3DAutoMove = false;
                    const toggleButton = document.getElementById('ice3DMoveToggle');
                    if (toggleButton) {
                        toggleButton.textContent = '启动';
                        toggleButton.style.background = '#28a745';
                    }
                    
                    // 清除之前的超时
                    if (userInteractionTimeout) {
                        clearTimeout(userInteractionTimeout);
                    }
                });
                
                // 监听控制器结束事件
                ice3DControls.addEventListener('end', function() {
                    // 5秒后自动恢复移动
                    userInteractionTimeout = setTimeout(() => {
                        ice3DAutoMove = true;
                        const toggleButton = document.getElementById('ice3DMoveToggle');
                        if (toggleButton) {
                            toggleButton.textContent = '暂停';
                            toggleButton.style.background = '#007bff';
                        }
                        console.log('用户交互结束，5秒后自动恢复上下移动');
                    }, 5000);
                });
            }
            
            // 处理窗口大小变化
            const debouncedResize = debounce(() => {
                const rect = container.getBoundingClientRect();
                ice3DCamera.aspect = rect.width / rect.height;
                ice3DCamera.updateProjectionMatrix();
                ice3DRenderer.setSize(rect.width, rect.height);

                // 处理Chart.js图表的resize
                if (window.liftChart) {
                    window.liftChart.resize();
                }
                if (window.dragChart) {
                    window.dragChart.resize();
                }

                // 处理2D冰形图表的resize
                const ice2DContainer = document.getElementById('ice2DContainer');
                const chartElement = document.getElementById('chartIcingShape');
                if (ice2DContainer && chartElement) {
                    // 重新计算Canvas尺寸
                    const containerRect = ice2DContainer.getBoundingClientRect();
                    const containerStyle = window.getComputedStyle(ice2DContainer);
                    const borderWidth = parseInt(containerStyle.borderLeftWidth) + parseInt(containerStyle.borderRightWidth);
                    const paddingWidth = parseInt(containerStyle.paddingLeft) + parseInt(containerStyle.paddingRight);
                    const borderHeight = parseInt(containerStyle.borderTopWidth) + parseInt(containerStyle.borderBottomWidth);
                    const paddingHeight = parseInt(containerStyle.paddingTop) + parseInt(containerStyle.paddingBottom);

                    let canvasWidth = containerRect.width - borderWidth - paddingWidth;
                    let canvasHeight = containerRect.height - borderHeight - paddingHeight;

                    // 应用宽高比优化
                    const aspectRatio = canvasWidth / canvasHeight;
                    const idealAspectRatio = 2.5;

                    if (aspectRatio > idealAspectRatio) {
                        const maxWidth = canvasHeight * idealAspectRatio;
                        canvasWidth = Math.min(canvasWidth, maxWidth);
                    } else if (aspectRatio < 1.8) {
                        const maxHeight = canvasWidth / 1.8;
                        canvasHeight = Math.min(canvasHeight, maxHeight);
                    }

                    // 更新Canvas尺寸
                    chartElement.style.width = canvasWidth + 'px';
                    chartElement.style.height = canvasHeight + 'px';

                    const devicePixelRatio = window.devicePixelRatio || 1;
                    chartElement.width = canvasWidth * devicePixelRatio;
                    chartElement.height = canvasHeight * devicePixelRatio;

                    // 重要：重新调整Canvas上下文缩放以确保清晰度
                    const ctx = chartElement.getContext('2d');
                    ctx.scale(devicePixelRatio, devicePixelRatio);

                    // 如果有Chart.js实例，触发重绘
                    if (window.icingShapeChart) {
                        window.icingShapeChart.resize();
                    }
                }
            }, 150);
            
            window.addEventListener('resize', debouncedResize);
        }
        
        // 显示示例3D冰形 - 仅清理场景，完全不添加任何数据
        function showSample3DIceShape() {
            if (!ice3DScene) return;
            
            console.log('3D场景完全清空，等待用户真实数据...');
            
            // 不删除任何测试立方体，因为已经不再创建测试立方体
            
            // 完全不创建任何示例数据，保持场景为空
            console.log('场景已完全清空，只使用用户的真实干净机翼数据');
            
            // 调整相机位置：恢复用户确认的理想角度
            ice3DCamera.position.set(-1.84, 1.31, -0.62);
            if (ice3DControls) {
                ice3DControls.target.set(0.02, 0, 0.12);
                ice3DControls.update();
            }
            
            console.log('3D场景已准备就绪，只使用用户真实数据渲染');
        }
        
        // Z轴堆叠算法：将2D坐标点连接成面，再堆叠成3D体
        function create3DIceGeometry(upperCoords, lowerCoords, thickness = 2.0, layers = 10) {
            const meshArray = [];
            
            console.log('创建3D体积冰形, 上表面点数:', upperCoords.length, '下表面点数:', lowerCoords.length);
            
            // 确保坐标数组有效
            if (!upperCoords || !lowerCoords || upperCoords.length < 3 || lowerCoords.length < 3) {
                console.warn('坐标数据不足，无法创建3D几何体');
                return meshArray;
            }

            try {
                const scale = 80; // 坐标缩放 - 增大显示
                
                // 创建完整的2D轮廓（上表面+下表面形成封闭区域）
                const shape = new THREE.Shape();
                
                // 从第一个上表面点开始
                if (upperCoords.length > 0) {
                    shape.moveTo(upperCoords[0].x * scale, upperCoords[0].y * scale);
                    
                    // 绘制完整的上表面
                    for (let i = 1; i < upperCoords.length; i++) {
                        shape.lineTo(upperCoords[i].x * scale, upperCoords[i].y * scale);
                    }
                    
                    // 连接到下表面的最后一个点
                    if (lowerCoords.length > 0) {
                        shape.lineTo(lowerCoords[lowerCoords.length - 1].x * scale, 
                                   lowerCoords[lowerCoords.length - 1].y * scale);
                        
                        // 绘制下表面（逆序连接）
                        for (let i = lowerCoords.length - 2; i >= 0; i--) {
                            shape.lineTo(lowerCoords[i].x * scale, lowerCoords[i].y * scale);
                        }
                    }
                    
                    // 闭合回到起始点
                    shape.lineTo(upperCoords[0].x * scale, upperCoords[0].y * scale);
                }
                
                // 沿Z轴堆叠创建3D体积
                for (let layer = 0; layer < layers; layer++) {
                    const zPosition = (layer / (layers - 1)) * thickness - thickness / 2;
                    
                    // 为每一层创建挤压几何体
                    const extrudeSettings = {
                        depth: thickness / layers, // 每层的厚度
                        bevelEnabled: false,
                        steps: 1
                    };
                    
                    const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
                    
                    // 设置Z位置
                    geometry.translate(0, 0, zPosition);
                    
                    // 根据层数设置材质透明度（底层更实，顶层更透明）
                    const opacity = 0.4 + (layer / layers) * 0.4;
                    const hue = 0.0; // 红色系（AI预测冰形）
                    
                    const material = new THREE.MeshLambertMaterial({
                        color: new THREE.Color().setHSL(hue, 0.8, 0.5),
                        transparent: true,
                        opacity: opacity,
                        side: THREE.DoubleSide
                    });
                    
                    const mesh = new THREE.Mesh(geometry, material);
                    mesh.castShadow = true;
                    mesh.receiveShadow = true;
                    
                    meshArray.push(mesh);
                    
                    console.log(`创建第${layer + 1}层，Z位置: ${zPosition.toFixed(3)}，透明度: ${opacity.toFixed(2)}`);
                }
                
                console.log(`成功创建${meshArray.length}层3D体积，总厚度: ${thickness}`);
                
            } catch (error) {
                console.error('创建3D几何体时出错:', error);
            }
            
            return meshArray;
        }
        
        // 创建特定类型的3D形状（干净机翼、实验冰形、AI预测）
        function create3DShapeByType(coordinates, shapeType, thickness = 1.0, layers = 5) {
            const meshArray = [];
            
            if (!coordinates || coordinates.length < 3) {
                console.warn(`${shapeType} 坐标数据不足`);
                return meshArray;
            }
            
            try {
                const scale = 80; // 坐标缩放 - 增大显示
                
                // 创建翼型形状
                const shape = new THREE.Shape();
                
                console.log(`开始创建${shapeType}翼型轮廓，共${coordinates.length}个点:`);
                
                // 移动到第一个点（前缘）
                shape.moveTo(coordinates[0].x * scale, coordinates[0].y * scale);
                console.log(`前缘点: (${coordinates[0].x.toFixed(6)}, ${coordinates[0].y.toFixed(6)})`);
                
                // 使用贝塞尔曲线创建平滑的翼型轮廓
                for (let i = 1; i < coordinates.length; i++) {
                    const currentPoint = coordinates[i];
                    const prevPoint = coordinates[i - 1];
                    
                    // 计算控制点以创建平滑曲线
                    const controlX = (prevPoint.x + currentPoint.x) / 2;
                    const controlY = (prevPoint.y + currentPoint.y) / 2;
                    
                    // 使用二次贝塞尔曲线连接点，创建平滑的翼型表面
                    shape.quadraticCurveTo(
                        controlX * scale, controlY * scale,
                        currentPoint.x * scale, currentPoint.y * scale
                    );
                    
                    if (i < 6 || i === coordinates.length - 1) {
                        console.log(`点${i}: (${currentPoint.x.toFixed(6)}, ${currentPoint.y.toFixed(6)})`);
                    }
                }
                
                // 用平滑曲线闭合形状回到前缘
                const lastPoint = coordinates[coordinates.length - 1];
                const firstPoint = coordinates[0];
                const closeControlX = (lastPoint.x + firstPoint.x) / 2;
                const closeControlY = (lastPoint.y + firstPoint.y) / 2;
                
                shape.quadraticCurveTo(
                    closeControlX * scale, closeControlY * scale,
                    firstPoint.x * scale, firstPoint.y * scale
                );
                
                console.log('翼型轮廓已平滑闭合');
                
                // Z轴层叠设置 - 根据层数参数决定
                const layerCount = layers;  // 使用传入的层数参数
                const singleLayerThickness = 0.05;  // 每层的固定厚度
                
                // 创建多层堆叠的翼型体积
                for (let layer = 0; layer < layerCount; layer++) {
                    // 不设置Z位置偏移，让外部函数控制每层的位置
                    
                    // 挤压设置，创建翼型体积
                    const extrudeSettings = {
                        depth: singleLayerThickness,     // 固定的单层厚度
                        bevelEnabled: true,              // 启用倒角创建更平滑的边缘
                        bevelThickness: 0.01,            // 减小倒角厚度
                        bevelSize: 0.01,                 // 减小倒角大小
                        bevelSegments: 2,                // 减少倒角分段数
                        steps: 1                         // 减少挤压步数
                    };
                    
                    const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
                    
                    // 不再在这里设置Z位置，让外部函数控制
                    // 每个单独的mesh都在原点创建
                    
                    // 根据形状类型设置材质颜色（半透明）- 使用更深的颜色
                    let color = 0x1a252f; // 默认深灰色（更深）
                    
                    if (shapeType === 'clean') {
                        color = 0x1f4e79;   // 深蓝色 - 干净翼型
                    } else if (shapeType === 'initial') {
                        color = 0xd68910;   // 深橙色 - 实验冰形
                    } else if (shapeType === 'predicted') {
                        color = 0xc0392b;   // 深红色 - AI预测
                    }
                    
                    // 材质设置 - 不透明，颜色深沉，无边框线
                    const material = new THREE.MeshPhongMaterial({
                        color: color,
                        transparent: false,  // 不透明
                        side: THREE.DoubleSide,
                        shininess: 50,
                        wireframe: false     // 确保不显示线框
                    });
                    
                    const mesh = new THREE.Mesh(geometry, material);
                    mesh.castShadow = true;
                    mesh.receiveShadow = true;
                    mesh.userData = { type: shapeType, layer: layer };
                    
                    meshArray.push(mesh);
                    
                    console.log(`第${layer + 1}层翼型: 单层厚度=${singleLayerThickness}, 不透明, 颜色=#${color.toString(16)}`);
                }
                
                console.log(`${shapeType} 翼型3D创建完成：${layerCount}层，单层模式=${layerCount === 1}`);
                
            } catch (error) {
                console.error(`创建${shapeType}翼型时出错:`, error);
            }
            
            return meshArray;
        }
        
        // 3D冰形生长动画
        function animate3DIceGrowth(meshes, duration = 3000) {
            if (!meshes || meshes.length === 0) return;
            
            const startTime = Date.now();
            
            // 初始化所有网格为不可见
            meshes.forEach(mesh => {
                mesh.scale.z = 0.01;
                mesh.material.opacity = 0;
            });
            
            function growthAnimation() {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // 计算当前应该显示的层数
                const visibleLayers = Math.floor(progress * meshes.length);
                
                meshes.forEach((mesh, index) => {
                    if (index <= visibleLayers) {
                        // 层级生长动画
                        const layerProgress = Math.min((progress * meshes.length - index), 1);
                        const easeProgress = 1 - Math.pow(1 - layerProgress, 3); // ease-out
                        
                        mesh.scale.z = 0.01 + easeProgress * 0.99;
                        mesh.material.opacity = mesh.material.userData.originalOpacity * easeProgress;
                    }
                });
                
                if (progress < 1) {
                    requestAnimationFrame(growthAnimation);
                }
            }
            
            // 保存原始透明度
            meshes.forEach(mesh => {
                mesh.material.userData.originalOpacity = mesh.material.opacity;
            });
            
            growthAnimation();
        }
        
        // 渲染3D冰形预测结果
        function render3DIcePrediction(data, showLoading = true) {
            if (!ice3DScene || !data) {
                console.warn('3D场景未初始化或数据无效');
                if (showLoading) {
                    hide3DIceLoading();
                }
                return;
            }
            
            if (showLoading) {
                update3DIceProgress('正在构建3D冰形几何体...', 70);
            }
            
            console.log('开始渲染3D冰形，数据:', data);
            console.log('数据类型检查:');
            console.log('- clean_body存在?', !!data.clean_body, '类型:', typeof data.clean_body, '长度:', data.clean_body?.length);
            console.log('- initial_shape存在?', !!data.initial_shape, '类型:', typeof data.initial_shape, '长度:', data.initial_shape?.length);
            console.log('- predicted_shape存在?', !!data.predicted_shape, '类型:', typeof data.predicted_shape, '长度:', data.predicted_shape?.length);
            
            // 清除之前的冰形网格和测试立方体
            ice3DMeshes.forEach(mesh => {
                ice3DScene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) mesh.material.dispose();
            });
            ice3DMeshes = [];
            
            // 彻底清除之前的所有3D对象
            console.log('开始彻底清除3D场景中的冰形对象...');
            
            // 先清除追踪的网格数组
            ice3DMeshes.forEach(mesh => {
                ice3DScene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) mesh.material.dispose();
            });
            ice3DMeshes = [];
            
            // 再次遍历整个场景，清除所有相关对象
            const objectsToRemove = [];
            ice3DScene.traverse(function(child) {
                if (child instanceof THREE.Mesh) {
                    // 清除所有翼型相关的网格
                    if (child.name && (child.name.includes('机翼') || child.name.includes('冰形') || child.name.includes('翼型'))) {
                        objectsToRemove.push(child);
                    }
                    // 清除所有ExtrudeGeometry（翼型几何体）
                    else if (child.geometry instanceof THREE.ExtrudeGeometry) {
                        objectsToRemove.push(child);
                    }
                    // 清除测试立方体
                    else if (child.geometry instanceof THREE.BoxGeometry && 
                             (child.material.color.getHex() === 0x4caf50 || child.material.color.getHex() === 0x00ff00)) {
                        objectsToRemove.push(child);
                    }
                }
            });
            
            // 执行清除
            objectsToRemove.forEach(obj => {
                ice3DScene.remove(obj);
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) obj.material.dispose();
                console.log('🗑️ 清除了对象:', obj.name || '未命名对象');
            });
            
            console.log(`✅ 彻底清除完成，共清除${objectsToRemove.length}个对象，scene.children数量：${ice3DScene.children.length}`);
            
            if (showLoading) {
                update3DIceProgress('正在生成3D网格...', 85);
            }
            
            try {
                // 不过滤数据，完全显示所有翼型坐标

                // 准备坐标数据
                const cleanBodyCoords = data.clean_body ? 
                    data.clean_body.map(point => ({ x: point[0], y: point[1] })) : [];
                const initialShapeCoords = data.initial_shape ? 
                    data.initial_shape.map(point => ({ x: point[0], y: point[1] })) : [];
                const predictedShapeCoords = data.predicted_shape ? 
                    data.predicted_shape.map(point => ({ x: point[0], y: point[1] })) : [];

                // 创建干净机翼形状（底部）
                if (cleanBodyCoords.length >= 3) {
                    const cleanMeshes = create3DShapeByType(cleanBodyCoords, 'clean', 0.3, 5);
                    cleanMeshes.forEach((mesh, index) => {
                        mesh.position.set(0, 0, index * 0.05);  // 层间无间隙，从Z=0开始
                        mesh.name = '干净机翼';
                        ice3DScene.add(mesh);
                        ice3DMeshes.push(mesh);
                    });
                    console.log('✅ 添加干净机翼5层（深蓝色），Z位置：0 到 0.20');
                } else {
                    console.log('❌ 干净机翼数据不足，跳过创建');
                }

                // 创建实验冰形形状（中间）
                if (initialShapeCoords.length >= 3) {
                    const initialMeshes = create3DShapeByType(initialShapeCoords, 'initial', 0.3, 5);
                    initialMeshes.forEach((mesh, index) => {
                        mesh.position.set(0, 0, 0.25 + index * 0.05);  // 层间无间隙，从Z=0.25开始
                        mesh.name = '实验冰形';
                        ice3DScene.add(mesh);
                        ice3DMeshes.push(mesh);
                    });
                    console.log('添加实验冰形5层（深橙色），Z位置：0.25 到 0.45');
                } else {
                    console.log('❌ 实验冰形数据不足，跳过创建');
                }

                // 创建AI预测冰形形状（顶部）
                if (predictedShapeCoords.length >= 3) {
                    const predictedMeshes = create3DShapeByType(predictedShapeCoords, 'predicted', 0.3, 5);
                    predictedMeshes.forEach((mesh, index) => {
                        mesh.position.set(0, 0, 0.50 + index * 0.05);  // 层间无间隙，从Z=0.50开始
                        mesh.name = 'AI预测冰形';
                        ice3DScene.add(mesh);
                        ice3DMeshes.push(mesh);
                    });
                    console.log('添加AI预测冰形5层（深红色），Z位置：0.50 到 0.70');
                } else {
                    console.log('❌ AI预测冰形数据不足，跳过创建');
                }
                
                // 调整相机视角：恢复用户确认的理想角度  
                ice3DCamera.position.set(-1.84, 1.31, -0.62);  // 恢复用户确认的最佳角度
                if (ice3DControls) {
                    ice3DControls.target.set(0.02, 0, 0.4);  // 聚焦在Z轴中心
                    ice3DControls.update();
                }
                
                // 更新控制面板信息
                console.log(`3D翼型创建完成：${ice3DMeshes.length}个网格`);
                
                // 图例已经包含控制面板，无需额外操作
                console.log(`3D翼型创建完成：${ice3DMeshes.length}个网格，图例中的控制面板可用`);
                
                // 3D构建完成后隐藏加载状态
                if (showLoading) {
                    update3DIceProgress('3D冰形渲染完成', 100);
                    setTimeout(() => {
                        hide3DIceLoading();
                    }, 500); // 短延迟让用户看到完成状态
                }
                
                // 如果没有足够的数据，显示示例翼型
                if (ice3DMeshes.length === 0) {
                    console.warn('没有足够的翼型数据，显示示例翼型');
                    showSample3DIceShape();
                    if (showLoading) {
                        hide3DIceLoading();
                    }
                }
                
            } catch (error) {
                console.error('3D冰形渲染失败:', error);
                
                // 错误时隐藏加载状态
                if (showLoading) {
                    hide3DIceLoading();
                }
                
                // 显示错误信息
                const errorGeometry = new THREE.BoxGeometry(0.2, 0.2, 0.2);
                const errorMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
                const errorMesh = new THREE.Mesh(errorGeometry, errorMaterial);
                ice3DScene.add(errorMesh);
                ice3DMeshes.push(errorMesh);
            }
        }

        // 渲染STL模型
        function renderSTL() {
            console.log('开始初始化STL渲染器');
            const container = document.getElementById('stlContainer');
            const loadingElement = document.getElementById('stlLoading');
            const loadingText = document.getElementById('loadingText');
            const progressBar = document.getElementById('loadingProgressBar');
            
            if (!container) {
                console.error('未找到stlContainer容器');
                return;
            }
            console.log('stlContainer容器已找到');

            // 显示加载状态
            if (loadingElement) {
                loadingElement.style.display = 'flex';
                loadingElement.classList.remove('fade-out');
            }

            const width = container.clientWidth;
            const height = container.clientHeight;
            console.log(`容器尺寸: ${width} x ${height}`);

            // 检查Three.js是否加载
            if (typeof THREE === 'undefined') {
                console.error('Three.js未加载');
                if (loadingText) loadingText.textContent = 'Three.js加载失败';
                return;
            }
            console.log('Three.js已加载');
            
            // 更新加载进度
            if (progressBar) progressBar.style.width = '20%';
            if (loadingText) loadingText.textContent = '初始化3D场景...';

            // 创建场景、相机和渲染器
            const scene = new THREE.Scene();
            scene.background = new THREE.Color(0xffffff); // 纯白色背景
            
            const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
            camera.position.set(0, 0, 5);
            
            const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            renderer.setSize(width, height);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            
            // 设置渲染器样式以避免闪烁
            renderer.domElement.style.position = 'absolute';
            renderer.domElement.style.top = '0';
            renderer.domElement.style.left = '0';
            renderer.domElement.style.opacity = '0';
            renderer.domElement.style.transition = 'opacity 0.5s ease-in-out';
            
            // 保存参数面板并清空其他渲染器内容，然后添加新渲染器
            const airfoilInfo = container.querySelector('.airfoil-info');
            const existingCanvas = container.querySelectorAll('canvas');
            existingCanvas.forEach(canvas => canvas.remove());
            
            container.appendChild(renderer.domElement);
            
            // 确保参数面板在最上层
            if (airfoilInfo) {
                container.appendChild(airfoilInfo);
            }
            console.log('渲染器已初始化并添加到容器，参数面板已保留');
            
            // 更新加载进度
            if (progressBar) progressBar.style.width = '40%';
            if (loadingText) loadingText.textContent = '添加光照效果...';

            // 添加光照
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(1, 1, 1).normalize();
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            console.log('光照已添加');

            // 检查STLLoader是否可用
            if (typeof THREE.STLLoader === 'undefined' || !stlLoaderReady) {
                console.error('STLLoader未加载或未准备就绪，显示备用几何体');
                console.log('stlLoaderReady状态:', stlLoaderReady);
                console.log('THREE.STLLoader类型:', typeof THREE.STLLoader);
                
                // 更新加载状态
                if (progressBar) progressBar.style.width = '60%';
                if (loadingText) loadingText.textContent = '创建备用模型...';
                
                // 创建一个简单的翼型形状作为备用
                const shape = new THREE.Shape();
                shape.moveTo(0, 0);
                shape.bezierCurveTo(0.5, 0.2, 0.5, -0.2, 1, 0);
                shape.bezierCurveTo(0.5, -0.1, 0.5, 0.1, 0, 0);
                
                const extrudeSettings = { depth: 0.1, bevelEnabled: false };
                const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
                const material = new THREE.MeshPhongMaterial({ color: 0x0077ff });
                const mesh = new THREE.Mesh(geometry, material);
                
                mesh.position.set(-0.5, 0, 0);
                scene.add(mesh);
                
                // 完成加载进度
                if (progressBar) progressBar.style.width = '100%';
                if (loadingText) loadingText.textContent = '模型加载完成';
                
                // 显示渲染器并隐藏加载状态
                setTimeout(() => {
                    renderer.domElement.style.opacity = '1';
                    if (loadingElement) {
                        loadingElement.classList.add('fade-out');
                        setTimeout(() => {
                            loadingElement.style.display = 'none';
                        }, 500);
                    }
                }, 300);
                
                // 检查OrbitControls是否可用
                if (typeof THREE.OrbitControls !== 'undefined' && orbitControlsReady) {
                    // 为备用几何体也添加轨道控制器
                    const controls = new THREE.OrbitControls(camera, renderer.domElement);
                    controls.enableDamping = true;
                    controls.dampingFactor = 0.05;
                    controls.screenSpacePanning = false;
                    controls.minDistance = 1;
                    controls.maxDistance = 30; // 调整以适应更大的视野
                    controls.autoRotate = true;
                    controls.autoRotateSpeed = 1.0; // 降低旋转速度
                    
                    function animate() {
                        requestAnimationFrame(animate);
                        controls.update();
                        renderer.render(scene, camera);
                    }
                    animate();
                } else {
                    console.log('OrbitControls不可用，使用简单旋转');
                    function animate() {
                        requestAnimationFrame(animate);
                        mesh.rotation.y += 0.01; // 降低旋转速度
                        renderer.render(scene, camera);
                    }
                    animate();
                }
                
                // 显示提示信息
                const infoDiv = document.createElement('div');
                infoDiv.style.cssText = `
                    position: absolute; 
                    top: 10px; 
                    left: 10px; 
                    background: rgba(255,255,255,0.9); 
                    padding: 10px; 
                    border-radius: 5px; 
                    font-size: 12px; 
                    color: #666;
                    max-width: 200px;
                `;
                infoDiv.innerHTML = '正在显示备用翼型模型<br/>STL加载器加载中...';
                container.appendChild(infoDiv);
                
                return;
            }
            console.log('STLLoader已加载且准备就绪');
            
            // 更新加载进度
            if (progressBar) progressBar.style.width = '60%';
            if (loadingText) loadingText.textContent = '开始加载STL文件...';

            // 加载STL文件，添加缓存破坏参数
            const loader = new THREE.STLLoader();
            const stlPath = '/static/naca0012.stl?t=' + Date.now(); // 添加时间戳避免缓存
            console.log('开始加载STL文件:', stlPath);
            
            loader.load(stlPath, 
                function(geometry) {
                    console.log('STL文件加载成功');
                    console.log('几何体顶点数:', geometry.attributes.position.count);
                    
                    // 更新加载进度
                    if (progressBar) progressBar.style.width = '80%';
                    if (loadingText) loadingText.textContent = '处理模型数据...';
                    
                    // 计算几何体的边界框以进行适当的缩放和定位
                    geometry.computeBoundingBox();
                    const box = geometry.boundingBox;
                    const center = box.getCenter(new THREE.Vector3());
                    const size = box.getSize(new THREE.Vector3());
                    
                    console.log('模型尺寸:', size);
                    console.log('模型中心:', center);
                    
                    // 为不同部件创建多种颜色
                    // 首先计算顶点颜色数组
                    const positions = geometry.attributes.position;
                    const colors = new Float32Array(positions.count * 3);
                    
                    // 定义颜色方案：根据Y坐标分配不同颜色
                    const color1 = new THREE.Color(0xff4444); // 红色 - 上表面
                    const color2 = new THREE.Color(0x4444ff); // 蓝色 - 下表面  
                    const color3 = new THREE.Color(0x44ff44); // 绿色 - 中间区域
                    const color4 = new THREE.Color(0xffaa44); // 橙色 - 前缘
                    const color5 = new THREE.Color(0xaa44ff); // 紫色 - 后缘
                    
                    // 计算Y坐标的范围以进行颜色映射
                    let minY = Infinity, maxY = -Infinity;
                    let minX = Infinity, maxX = -Infinity;
                    for (let i = 0; i < positions.count; i++) {
                        const y = positions.getY(i);
                        const x = positions.getX(i);
                        minY = Math.min(minY, y);
                        maxY = Math.max(maxY, y);
                        minX = Math.min(minX, x);
                        maxX = Math.max(maxX, x);
                    }
                    
                    console.log(`Y坐标范围: ${minY} 到 ${maxY}`);
                    console.log(`X坐标范围: ${minX} 到 ${maxX}`);
                    
                    // 为每个顶点分配颜色
                    for (let i = 0; i < positions.count; i++) {
                        const x = positions.getX(i);
                        const y = positions.getY(i);
                        const z = positions.getZ(i);
                        
                        // 归一化坐标
                        const normalizedY = (y - minY) / (maxY - minY);
                        const normalizedX = (x - minX) / (maxX - minX);
                        
                        let finalColor;
                        
                        // 根据位置选择颜色
                        if (normalizedX < 0.2) {
                            // 前缘区域 - 橙色
                            finalColor = color4;
                        } else if (normalizedX > 0.8) {
                            // 后缘区域 - 紫色
                            finalColor = color5;
                        } else if (normalizedY > 0.6) {
                            // 上表面 - 红色
                            finalColor = color1;
                        } else if (normalizedY < 0.4) {
                            // 下表面 - 蓝色
                            finalColor = color2;
                        } else {
                            // 中间区域 - 绿色
                            finalColor = color3;
                        }
                        
                        // 设置顶点颜色
                        colors[i * 3] = finalColor.r;
                        colors[i * 3 + 1] = finalColor.g;
                        colors[i * 3 + 2] = finalColor.b;
                    }
                    
                    // 将颜色属性添加到几何体
                    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
                    
                    // 创建支持顶点颜色的材质
                    const material = new THREE.MeshPhongMaterial({ 
                        vertexColors: true, // 启用顶点颜色
                        specular: 0x111111, 
                        shininess: 200,
                        side: THREE.DoubleSide // 双面渲染以确保所有面都可见
                    });
                    
                    const mesh = new THREE.Mesh(geometry, material);
                    console.log('已为STL模型应用多种颜色');
                    
                    // 保存全局引用以便动画联动
                    stlMesh = mesh;
                    stlScene = scene;
                    stlCamera = camera;
                    stlRenderer = renderer;
                    
                    // 将模型居中
                    mesh.position.sub(center);
                    
                    // 计算适当的缩放比例
                    const maxDim = Math.max(size.x, size.y, size.z);
                    console.log('最大尺寸:', maxDim);
                    
                    // 统一缩放模型到合适大小（目标最大尺寸为8个单位，更大显示）
                    const targetSize =21;
                    const scale = targetSize / maxDim;
                    mesh.scale.setScalar(scale);
                    console.log('模型缩放比例:', scale);
                    
                    // 确保模型在原点
                    mesh.position.set(0, 0, 0);
                    console.log('模型位置:', mesh.position);
                    
                    // 设置固定的相机位置，适合观察缩放后的模型（调整相机距离以适应更大的模型）
                    camera.position.set(0, 0, 15);
                    camera.lookAt(0, 0, 0);
                    console.log('相机位置:', camera.position);
                    
                    scene.add(mesh);
                    console.log('STL模型已添加到场景');
                    
                    // 初始化冰点粒子系统
                    iceParticleSystem = createIceParticleSystem(scene);
                    
                    // 初始化气流可视化粒子系统
                    airflowParticleSystem = createAirflowParticleSystem(scene);
                    
                    // 初始化增强环境光照系统
                    environmentLights = createEnvironmentLights(scene);
                    
                    // 为模型创建霜冻效果
                    createFrostEffect(mesh);
                    
                    // 完成加载进度
                    if (progressBar) progressBar.style.width = '100%';
                    if (loadingText) loadingText.textContent = '模型加载完成';
                    
                    // 延迟显示渲染器并隐藏加载状态，确保模型已完全渲染
                    setTimeout(() => {
                        renderer.domElement.style.opacity = '1';
                        if (loadingElement) {
                            loadingElement.classList.add('fade-out');
                            setTimeout(() => {
                                loadingElement.style.display = 'none';
                            }, 500);
                        }
                    }, 300);

                    // 检查OrbitControls是否可用并添加交互功能
                    if (typeof THREE.OrbitControls !== 'undefined' && orbitControlsReady) {
                        const controls = new THREE.OrbitControls(camera, renderer.domElement);
                        controls.enableDamping = true; // 启用阻尼效果
                        controls.dampingFactor = 0.05;
                        controls.screenSpacePanning = false;
                        controls.minDistance = 6; // 最小缩放距离（适应更大模型）
                        controls.maxDistance = 120; // 最大缩放距离（适应更大模型）
                        controls.maxPolarAngle = Math.PI; // 垂直旋转限制
                        controls.autoRotate = true; // 自动旋转
                        controls.autoRotateSpeed = 1.0; // 初始旋转速度
                        
                        // 保存控制器引用
                        stlControls = controls;
                        console.log('轨道控制器已添加');

                        // 增强动画循环（带工况联动效果）
                        function animate() {
                            requestAnimationFrame(animate);
                            
                            // 湍流效果：根据液态水含量添加随机晃动
                            if (stlTurbulenceEffect > 0) {
                                const time = Date.now() * 0.005;
                                const turbulenceX = Math.sin(time * 1.2) * stlTurbulenceEffect * 0.02;
                                const turbulenceY = Math.cos(time * 0.8) * stlTurbulenceEffect * 0.01;
                                const turbulenceZ = Math.sin(time * 1.5) * stlTurbulenceEffect * 0.015;
                                
                                mesh.rotation.x += turbulenceX;
                                mesh.rotation.y += turbulenceY;
                                mesh.rotation.z += turbulenceZ;
                            }
                            
                            // 结冰效果：轻微的"冻结"感（减慢运动）
                            if (stlIcingEffect > 0) {
                                const iceFactor = 1 - stlIcingEffect * 0.3; // 最多减慢30%
                                controls.autoRotateSpeed = stlAnimationSpeed * iceFactor;
                            } else {
                                controls.autoRotateSpeed = stlAnimationSpeed;
                            }
                            
                            // 更新冰点粒子动画
                            updateIceParticles();
                            
                            // 更新气流粒子动画
                            updateAirflowParticles();
                            
                            controls.update(); // 更新控制器
                            renderer.render(scene, camera);
                        }
                        animate();
                        console.log('🎬 增强动画已启动（带工况联动效果）');
                    } else {
                        console.log('OrbitControls不可用，使用增强简单动画');
                        // 增强简单旋转动画
                        function animate() {
                            requestAnimationFrame(animate);
                            
                            // 基础旋转（受速度影响）
                            mesh.rotation.y += 0.01 * stlAnimationSpeed;
                            
                            // 湍流效果
                            if (stlTurbulenceEffect > 0) {
                                const time = Date.now() * 0.005;
                                mesh.rotation.x = Math.sin(time * 1.2) * stlTurbulenceEffect * 0.02;
                                mesh.rotation.z = Math.sin(time * 1.5) * stlTurbulenceEffect * 0.015;
                            }
                            
                            // 更新冰点粒子动画
                            updateIceParticles();
                            
                            // 更新气流粒子动画
                            updateAirflowParticles();
                            
                            renderer.render(scene, camera);
                        }
                        animate();
                        console.log('🎬 增强简单动画已启动（带工况联动效果）');
                    }
                },
                function(progress) {
                    if (progress.total > 0) {
                        const percent = Math.round(progress.loaded / progress.total * 100);
                        console.log('加载进度:', percent + '%');
                        // 更新进度条 (60% + 20% * 实际进度)
                        if (progressBar) {
                            progressBar.style.width = (60 + percent * 0.2) + '%';
                        }
                        if (loadingText) {
                            loadingText.textContent = `正在加载STL文件 ${percent}%...`;
                        }
                    } else {
                        console.log('正在加载STL文件...');
                        if (loadingText) loadingText.textContent = '正在加载STL文件...';
                    }
                },
                function(error) {
                    console.error('加载STL文件失败:', error);
                    console.error('错误详情:', error.message || error);
                    
                    // 更新加载状态为错误
                    if (loadingText) loadingText.textContent = 'STL文件加载失败';
                    if (progressBar) {
                        progressBar.style.background = 'linear-gradient(90deg, #ff6b6b 0%, #ee5a24 100%)';
                        progressBar.style.width = '100%';
                    }
                    
                    // 保存参数面板
                    const airfoilInfo = container.querySelector('.airfoil-info');
                    
                    // 显示更详细的错误信息
                    const errorDiv = document.createElement('div');
                    errorDiv.style.cssText = `
                        display: flex; 
                        flex-direction: column; 
                        align-items: center; 
                        justify-content: center; 
                        height: 100%; 
                        color: #e74c3c; 
                        text-align: center; 
                        padding: 20px;
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(255,255,255,0.9);
                        z-index: 100;
                    `;
                    errorDiv.innerHTML = `
                        <h4>STL文件加载失败</h4>
                        <p>文件路径: ${stlPath}</p>
                        <p>错误信息: ${error.message || '未知错误'}</p>
                        <button onclick="renderSTL()" style="padding: 10px 20px; margin-top: 10px; background: #007BFF; color: white; border: none; border-radius: 5px; cursor: pointer;">重新加载</button>
                    `;
                    
                    // 清除现有的错误信息（如果有）
                    const existingError = container.querySelector('.error-message');
                    if (existingError) {
                        existingError.remove();
                    }
                    
                    errorDiv.className = 'error-message';
                    container.appendChild(errorDiv);
                    
                    // 确保参数面板仍然可见
                    if (airfoilInfo) {
                        container.appendChild(airfoilInfo);
                    }
                }
            );
        }

        // 预测次数计数器
        function updatePredictionCount() {
            let count = localStorage.getItem('predictionCount') || 0;
            count = parseInt(count) + 1;
            localStorage.setItem('predictionCount', count);
            document.getElementById('prediction-count').textContent = count;
        }

        // 初始化预测计数器
        function initPredictionCount() {
            const count = localStorage.getItem('predictionCount') || 0;
            document.getElementById('prediction-count').textContent = count;
        }

        // 页面加载时初始化计数器
        window.addEventListener('load', function() {
            initPredictionCount();
        });

        // 图表工具栏功能 - 导出数据
        function exportChartData(type) {
            const chartMap = {
                'lift': window.liftChart,
                'drag': window.dragChart,
                'water': window.waterChart,
                'heat': window.heatChart
            };

            const typeNames = {
                'lift': '升力系数',
                'drag': '阻力系数',
                'water': '水滴收集系数',
                'heat': '对流换热系数'
            };

            const chart = chartMap[type];
            if (chart && chart.data) {
                const data = {
                    timestamp: new Date().toISOString(),
                    type: typeNames[type],
                    conditions: {
                        velocity: document.getElementById('velocity').value || 'N/A',
                        temperature: document.getElementById('temperature').value || 'N/A',
                        lwc: document.getElementById('lwc').value || 'N/A',
                        mvd: document.getElementById('mvd').value || 'N/A'
                    },
                    data: {
                        labels: chart.data.labels,
                        datasets: chart.data.datasets.map(dataset => ({
                            label: dataset.label,
                            data: dataset.data
                        }))
                    }
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.download = `${type}_coefficient_data_${new Date().toISOString().slice(0, 10)}.json`;
                link.href = url;
                link.click();
                URL.revokeObjectURL(url);

                // 显示成功提示
                showNotification(`${typeNames[type]}数据已导出`, 'success');
            } else {
                showNotification('暂无数据可导出，请先进行预测', 'error');
            }
        }





        // 操作按钮功能
        function exportAllData() {
            const data = {
                timestamp: new Date().toISOString(),
                conditions: {
                    velocity: document.getElementById('velocity').value,
                    temperature: document.getElementById('temperature').value,
                    lwc: document.getElementById('lwc').value,
                    mvd: document.getElementById('mvd').value
                },
                charts: {
                    lift: window.liftChart ? window.liftChart.data : null,
                    drag: window.dragChart ? window.dragChart.data : null
                }
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = `experiment_data_${new Date().toISOString().slice(0, 10)}.json`;
            link.href = url;
            link.click();
            URL.revokeObjectURL(url);

            showNotification('实验数据已导出', 'success');
        }



        // 初始化3D水滴收集系数可视化
        function init3DWaterVisualization() {
            const container = document.getElementById('water3DContainer');
            if (!container) {
                console.error('找不到water3DContainer容器');
                return;
            }

            console.log('开始初始化3D水滴收集系数场景...');
            show3DHTCBetaLoading('water', '正在初始化3D场景...', 20);

            // 检查Three.js是否可用
            if (typeof THREE === 'undefined') {
                console.error('Three.js未加载');
                hide3DHTCBetaLoading('water');
                return;
            }

            // 清理之前的场景
            if (water3DControls) {
                water3DControls.dispose();
                water3DControls = null;
            }
            if (water3DRenderer) {
                water3DRenderer.dispose();
                water3DRenderer = null;
            }
            if (water3DAnimationId) {
                cancelAnimationFrame(water3DAnimationId);
                water3DAnimationId = null;
            }
            // 清理之前的网格
            water3DMeshes.forEach(mesh => {
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) {
                    if (Array.isArray(mesh.material)) {
                        mesh.material.forEach(mat => mat.dispose());
                    } else {
                        mesh.material.dispose();
                    }
                }
            });
            water3DMeshes = [];

            // 创建场景
            water3DScene = new THREE.Scene();
            water3DScene.background = new THREE.Color(0xffffff); // 纯白色背景

            // 创建相机 - 调整视角和位置以获得更大的视图
            const containerRect = container.getBoundingClientRect();
            water3DCamera = new THREE.PerspectiveCamera(45, containerRect.width / containerRect.height, 0.01, 100);
            water3DCamera.position.set(0.8, 0.6, 0.8); // 更近的距离，更大的视图

            // 创建渲染器
            const canvas = document.getElementById('water3DCanvas');
            // 清理canvas容器中的所有子元素
            while (canvas.firstChild) {
                canvas.removeChild(canvas.firstChild);
            }
            water3DRenderer = new THREE.WebGLRenderer({ antialias: true });
            water3DRenderer.setSize(containerRect.width, containerRect.height);
            water3DRenderer.shadowMap.enabled = true;
            water3DRenderer.shadowMap.type = THREE.PCFSoftShadowMap;
            canvas.appendChild(water3DRenderer.domElement);

            // 创建控制器 - 优化控制参数
            if (typeof THREE.OrbitControls !== 'undefined') {
                // 确保渲染器DOM元素已经添加到容器中
                setTimeout(() => {
                    try {
                        water3DControls = new THREE.OrbitControls(water3DCamera, water3DRenderer.domElement);
                        water3DControls.enableDamping = true;
                        water3DControls.dampingFactor = 0.05;
                        water3DControls.minDistance = 0.3; // 更小的最小距离，允许更近观察
                        water3DControls.maxDistance = 8; // 适中的最大距离
                        water3DControls.target.set(0, 0, 0.075); // 聚焦到3D形状中心

                        // 强制启用所有交互
                        water3DControls.enabled = true;
                        water3DControls.enableRotate = true;
                        water3DControls.enableZoom = true;
                        water3DControls.enablePan = true;

                        console.log('水滴收集系数3D控制器已创建，交互已启用');
                        console.log('控制器状态:', {
                            enabled: water3DControls.enabled,
                            rotate: water3DControls.enableRotate,
                            zoom: water3DControls.enableZoom,
                            pan: water3DControls.enablePan
                        });
                    } catch (error) {
                        console.error('创建水滴收集系数3D控制器失败:', error);
                    }
                }, 200);
            } else {
                console.warn('OrbitControls不可用，无法创建水滴收集系数3D控制器');
            }

            // 添加光照
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            water3DScene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5, 5, 5);
            directionalLight.castShadow = true;
            water3DScene.add(directionalLight);

            update3DHTCBetaProgress('water', '正在生成3D网格...', 60);

            // 生成3D表面
            render3DWaterSurface();

            // 开始动画循环
            animate3DWater();

            update3DHTCBetaProgress('water', '3D水滴收集系数可视化完成', 100);
            setTimeout(() => hide3DHTCBetaLoading('water'), 500);
        }

        // 后台初始化3D水滴收集系数可视化（不显示，用于预加载）
        function init3DWaterVisualizationBackground() {
            if (!lastHTCBetaData) return;

            console.log('后台预加载3D水滴收集系数场景...');

            // 检查Three.js是否可用
            if (typeof THREE === 'undefined') {
                console.error('Three.js未加载');
                return;
            }

            // 创建场景（如果还没有）
            if (!water3DScene) {
                water3DScene = new THREE.Scene();
                water3DScene.background = new THREE.Color(0xffffff);
            }

            // 预生成3D表面数据
            render3DWaterSurface();

            console.log('3D水滴收集系数场景预加载完成');
        }

        // 渲染3D水滴收集系数表面
        function render3DWaterSurface() {
            if (!lastHTCBetaData || !lastHTCBetaData.coordinates || !lastHTCBetaData.beta) {
                console.error('没有水滴收集系数数据');
                return;
            }

            // 清理之前的网格
            water3DMeshes.forEach(mesh => water3DScene.remove(mesh));
            water3DMeshes = [];

            const coordinates = lastHTCBetaData.coordinates;
            const betaValues = lastHTCBetaData.beta.values;
            const stats = lastHTCBetaData.beta.stats;

            console.log('开始创建3D水滴收集系数堆叠形状...');

            // 创建堆叠的3D形状，类似冰形
            const layers = 12; // 增加层数以获得更平滑的效果
            const totalThickness = 0.15; // 总厚度
            const layerThickness = totalThickness / layers; // 每层厚度

            for (let layer = 0; layer < layers; layer++) {
                // 为每一层创建形状
                const layerCoords = coordinates.map((coord, index) => {
                    const betaValue = betaValues[index];
                    const normalizedValue = (betaValue - stats.min) / (stats.max - stats.min);

                    // Z位置：紧密堆叠，无空隙
                    const zPosition = layer * layerThickness;

                    return {
                        x: coord[0],
                        y: coord[1],
                        z: zPosition,
                        betaValue: betaValue,
                        normalizedValue: normalizedValue,
                        layer: layer
                    };
                });

                // 创建该层的网格
                const layerMeshes = create3DWaterLayer(layerCoords, layer, layers, layerThickness);
                layerMeshes.forEach(mesh => {
                    water3DScene.add(mesh);
                    water3DMeshes.push(mesh);
                });
            }

            console.log(`已创建${layers}层水滴收集系数3D形状，共${coordinates.length}个点`);
        }

        // 创建3D水滴收集系数层
        function create3DWaterLayer(layerCoords, layerIndex, totalLayers, layerThickness) {
            const meshArray = [];

            // 使用ExtrudeGeometry创建更真实的翼型形状
            if (layerCoords.length < 3) return meshArray;

            // 创建翼型轮廓的Shape
            const shape = new THREE.Shape();

            // 移动到第一个点
            shape.moveTo(layerCoords[0].x, layerCoords[0].y);

            // 连接所有点形成轮廓
            for (let i = 1; i < layerCoords.length; i++) {
                shape.lineTo(layerCoords[i].x, layerCoords[i].y);
            }

            // 闭合轮廓
            shape.lineTo(layerCoords[0].x, layerCoords[0].y);

            // 挤出设置 - 无空隙紧密堆叠
            const extrudeSettings = {
                depth: layerThickness, // 使用传入的层厚度
                bevelEnabled: false
            };

            // 创建挤出几何体
            const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);

            // 为每个顶点设置颜色，增强系数差异的可视化
            const colors = [];
            const positionAttribute = geometry.getAttribute('position');

            for (let i = 0; i < positionAttribute.count; i++) {
                // 获取顶点位置
                const x = positionAttribute.getX(i);
                const y = positionAttribute.getY(i);

                // 找到最近的原始点来获取Beta值
                let minDistance = Infinity;
                let closestCoord = layerCoords[0];

                for (const coord of layerCoords) {
                    const distance = Math.sqrt((x - coord.x) ** 2 + (y - coord.y) ** 2);
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestCoord = coord;
                    }
                }

                // 增强颜色对比度，使系数差异更明显
                const normalizedValue = closestCoord.normalizedValue;

                // 使用更大的颜色范围和对比度
                let hue, saturation, lightness;

                if (normalizedValue < 0.3) {
                    // 低值：深绿色
                    hue = 0.33; // 绿色
                    saturation = 0.8;
                    lightness = 0.2 + normalizedValue * 0.3; // 20%-35%
                } else if (normalizedValue < 0.7) {
                    // 中值：亮绿色
                    hue = 0.33;
                    saturation = 0.9;
                    lightness = 0.4 + normalizedValue * 0.3; // 40%-61%
                } else {
                    // 高值：黄绿色，更亮
                    hue = 0.25; // 偏向黄绿
                    saturation = 1.0;
                    lightness = 0.6 + normalizedValue * 0.3; // 60%-81%
                }

                const color = new THREE.Color();
                color.setHSL(hue, saturation, lightness);
                colors.push(color.r, color.g, color.b);
            }

            geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));

            // 创建材质 - 减少透明度，增强可见性
            const material = new THREE.MeshLambertMaterial({
                vertexColors: true,
                transparent: true,
                opacity: 0.7 + (layerIndex / totalLayers) * 0.2, // 70%-90%透明度
                side: THREE.DoubleSide
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.z = layerIndex * layerThickness; // 无空隙堆叠
            mesh.name = `水滴收集系数层${layerIndex + 1}`;
            meshArray.push(mesh);

            return meshArray;
        }

        // 3D水滴收集系数动画循环
        function animate3DWater() {
            if (!water3DViewActive) return;

            water3DAnimationId = requestAnimationFrame(animate3DWater);

            // 检查容器大小变化并调整渲染器
            const container = document.getElementById('water3DContainer');
            if (container && water3DRenderer && water3DCamera) {
                const containerRect = container.getBoundingClientRect();
                const currentWidth = water3DRenderer.domElement.width;
                const currentHeight = water3DRenderer.domElement.height;

                if (Math.abs(containerRect.width - currentWidth) > 1 || Math.abs(containerRect.height - currentHeight) > 1) {
                    water3DCamera.aspect = containerRect.width / containerRect.height;
                    water3DCamera.updateProjectionMatrix();
                    water3DRenderer.setSize(containerRect.width, containerRect.height);
                }
            }

            if (water3DControls) {
                water3DControls.update();
            }

            if (water3DRenderer && water3DScene && water3DCamera) {
                water3DRenderer.render(water3DScene, water3DCamera);
            }
        }

        // 3D加载状态管理函数
        function show3DHTCBetaLoading(type, message, progress) {
            const loadingId = type === 'water' ? 'water3DLoading' : 'heat3DLoading';
            const loadingElement = document.getElementById(loadingId);

            if (loadingElement) {
                const textElement = loadingElement.querySelector('.loading-text');
                if (textElement && message) {
                    textElement.textContent = message;
                }

                loadingElement.style.display = 'flex';
                loadingElement.classList.remove('fade-out');

                if (progress !== undefined) {
                    update3DHTCBetaProgress(type, message, progress);
                }
            }
        }

        function hide3DHTCBetaLoading(type) {
            const loadingId = type === 'water' ? 'water3DLoading' : 'heat3DLoading';
            const loadingElement = document.getElementById(loadingId);

            if (loadingElement) {
                loadingElement.classList.add('fade-out');
                setTimeout(() => {
                    loadingElement.style.display = 'none';
                }, 500);
            }
        }

        function update3DHTCBetaProgress(type, message, progress) {
            const progressBarId = type === 'water' ? 'water3DProgressBar' : 'heat3DProgressBar';
            const progressBar = document.getElementById(progressBarId);

            if (progressBar) {
                progressBar.style.width = progress + '%';
            }

            console.log(`${type}3D进度: ${message} ${progress}%`);
        }

        // 初始化3D对流换热系数可视化
        function init3DHeatVisualization() {
            const container = document.getElementById('heat3DContainer');
            if (!container) {
                console.error('找不到heat3DContainer容器');
                return;
            }

            console.log('开始初始化3D对流换热系数场景...');
            show3DHTCBetaLoading('heat', '正在初始化3D场景...', 20);

            // 检查Three.js是否可用
            if (typeof THREE === 'undefined') {
                console.error('Three.js未加载');
                hide3DHTCBetaLoading('heat');
                return;
            }

            // 清理之前的场景
            if (heat3DControls) {
                heat3DControls.dispose();
                heat3DControls = null;
            }
            if (heat3DRenderer) {
                heat3DRenderer.dispose();
                heat3DRenderer = null;
            }
            if (heat3DAnimationId) {
                cancelAnimationFrame(heat3DAnimationId);
                heat3DAnimationId = null;
            }
            // 清理之前的网格
            heat3DMeshes.forEach(mesh => {
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) {
                    if (Array.isArray(mesh.material)) {
                        mesh.material.forEach(mat => mat.dispose());
                    } else {
                        mesh.material.dispose();
                    }
                }
            });
            heat3DMeshes = [];

            // 创建场景
            heat3DScene = new THREE.Scene();
            heat3DScene.background = new THREE.Color(0xffffff); // 纯白色背景

            // 创建相机 - 调整视角和位置以获得更大的视图
            const containerRect = container.getBoundingClientRect();
            heat3DCamera = new THREE.PerspectiveCamera(45, containerRect.width / containerRect.height, 0.01, 100);
            heat3DCamera.position.set(0.8, 0.6, 0.8); // 更近的距离，更大的视图

            // 创建渲染器
            const canvas = document.getElementById('heat3DCanvas');
            // 清理canvas容器中的所有子元素
            while (canvas.firstChild) {
                canvas.removeChild(canvas.firstChild);
            }
            heat3DRenderer = new THREE.WebGLRenderer({ antialias: true });
            heat3DRenderer.setSize(containerRect.width, containerRect.height);
            heat3DRenderer.shadowMap.enabled = true;
            heat3DRenderer.shadowMap.type = THREE.PCFSoftShadowMap;
            canvas.appendChild(heat3DRenderer.domElement);

            // 创建控制器 - 优化控制参数
            if (typeof THREE.OrbitControls !== 'undefined') {
                // 确保渲染器DOM元素已经添加到容器中
                setTimeout(() => {
                    try {
                        heat3DControls = new THREE.OrbitControls(heat3DCamera, heat3DRenderer.domElement);
                        heat3DControls.enableDamping = true;
                        heat3DControls.dampingFactor = 0.05;
                        heat3DControls.minDistance = 0.3; // 更小的最小距离，允许更近观察
                        heat3DControls.maxDistance = 8; // 适中的最大距离
                        heat3DControls.target.set(0, 0, 0.075); // 聚焦到3D形状中心

                        // 强制启用所有交互
                        heat3DControls.enabled = true;
                        heat3DControls.enableRotate = true;
                        heat3DControls.enableZoom = true;
                        heat3DControls.enablePan = true;

                        console.log('对流换热系数3D控制器已创建，交互已启用');
                        console.log('控制器状态:', {
                            enabled: heat3DControls.enabled,
                            rotate: heat3DControls.enableRotate,
                            zoom: heat3DControls.enableZoom,
                            pan: heat3DControls.enablePan
                        });
                    } catch (error) {
                        console.error('创建对流换热系数3D控制器失败:', error);
                    }
                }, 200);
            } else {
                console.warn('OrbitControls不可用，无法创建对流换热系数3D控制器');
            }

            // 添加光照
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            heat3DScene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5, 5, 5);
            directionalLight.castShadow = true;
            heat3DScene.add(directionalLight);

            update3DHTCBetaProgress('heat', '正在生成3D网格...', 60);

            // 生成3D表面
            render3DHeatSurface();

            // 开始动画循环
            animate3DHeat();

            update3DHTCBetaProgress('heat', '3D对流换热系数可视化完成', 100);
            setTimeout(() => hide3DHTCBetaLoading('heat'), 500);
        }

        // 后台初始化3D对流换热系数可视化（不显示，用于预加载）
        function init3DHeatVisualizationBackground() {
            if (!lastHTCBetaData) return;

            console.log('后台预加载3D对流换热系数场景...');

            // 检查Three.js是否可用
            if (typeof THREE === 'undefined') {
                console.error('Three.js未加载');
                return;
            }

            // 创建场景（如果还没有）
            if (!heat3DScene) {
                heat3DScene = new THREE.Scene();
                heat3DScene.background = new THREE.Color(0xffffff);
            }

            // 预生成3D表面数据
            render3DHeatSurface();

            console.log('3D对流换热系数场景预加载完成');
        }

        // 渲染3D对流换热系数表面
        function render3DHeatSurface() {
            if (!lastHTCBetaData || !lastHTCBetaData.coordinates || !lastHTCBetaData.htc) {
                console.error('没有对流换热系数数据');
                return;
            }

            // 清理之前的网格
            heat3DMeshes.forEach(mesh => heat3DScene.remove(mesh));
            heat3DMeshes = [];

            const coordinates = lastHTCBetaData.coordinates;
            const htcValues = lastHTCBetaData.htc.values;
            const stats = lastHTCBetaData.htc.stats;

            console.log('开始创建3D对流换热系数堆叠形状...');

            // 创建堆叠的3D形状，类似冰形
            const layers = 12; // 增加层数以获得更平滑的效果
            const totalThickness = 0.15; // 总厚度
            const layerThickness = totalThickness / layers; // 每层厚度

            for (let layer = 0; layer < layers; layer++) {
                // 为每一层创建形状
                const layerCoords = coordinates.map((coord, index) => {
                    const htcValue = htcValues[index];
                    const normalizedValue = (htcValue - stats.min) / (stats.max - stats.min);

                    // Z位置：紧密堆叠，无空隙
                    const zPosition = layer * layerThickness;

                    return {
                        x: coord[0],
                        y: coord[1],
                        z: zPosition,
                        htcValue: htcValue,
                        normalizedValue: normalizedValue,
                        layer: layer
                    };
                });

                // 创建该层的网格
                const layerMeshes = create3DHeatLayer(layerCoords, layer, layers, layerThickness);
                layerMeshes.forEach(mesh => {
                    heat3DScene.add(mesh);
                    heat3DMeshes.push(mesh);
                });
            }

            console.log(`已创建${layers}层对流换热系数3D形状，共${coordinates.length}个点`);
        }

        // 创建3D对流换热系数层
        function create3DHeatLayer(layerCoords, layerIndex, totalLayers, layerThickness) {
            const meshArray = [];

            // 使用ExtrudeGeometry创建更真实的翼型形状
            if (layerCoords.length < 3) return meshArray;

            // 创建翼型轮廓的Shape
            const shape = new THREE.Shape();

            // 移动到第一个点
            shape.moveTo(layerCoords[0].x, layerCoords[0].y);

            // 连接所有点形成轮廓
            for (let i = 1; i < layerCoords.length; i++) {
                shape.lineTo(layerCoords[i].x, layerCoords[i].y);
            }

            // 闭合轮廓
            shape.lineTo(layerCoords[0].x, layerCoords[0].y);

            // 挤出设置 - 无空隙紧密堆叠
            const extrudeSettings = {
                depth: layerThickness, // 使用传入的层厚度
                bevelEnabled: false
            };

            // 创建挤出几何体
            const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);

            // 为每个顶点设置颜色，增强系数差异的可视化
            const colors = [];
            const positionAttribute = geometry.getAttribute('position');

            for (let i = 0; i < positionAttribute.count; i++) {
                // 获取顶点位置
                const x = positionAttribute.getX(i);
                const y = positionAttribute.getY(i);

                // 找到最近的原始点来获取HTC值
                let minDistance = Infinity;
                let closestCoord = layerCoords[0];

                for (const coord of layerCoords) {
                    const distance = Math.sqrt((x - coord.x) ** 2 + (y - coord.y) ** 2);
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestCoord = coord;
                    }
                }

                // 增强颜色对比度，使系数差异更明显
                const normalizedValue = closestCoord.normalizedValue;

                // 使用更大的颜色范围和对比度
                let hue, saturation, lightness;

                if (normalizedValue < 0.3) {
                    // 低值：深橙色
                    hue = 0.08; // 橙色
                    saturation = 0.8;
                    lightness = 0.3 + normalizedValue * 0.2; // 30%-36%
                } else if (normalizedValue < 0.7) {
                    // 中值：亮橙色
                    hue = 0.05; // 偏红橙色
                    saturation = 0.9;
                    lightness = 0.4 + normalizedValue * 0.3; // 40%-61%
                } else {
                    // 高值：红色，更亮
                    hue = 0.0; // 红色
                    saturation = 1.0;
                    lightness = 0.5 + normalizedValue * 0.3; // 50%-71%
                }

                const color = new THREE.Color();
                color.setHSL(hue, saturation, lightness);
                colors.push(color.r, color.g, color.b);
            }

            geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));

            // 创建材质 - 减少透明度，增强可见性
            const material = new THREE.MeshLambertMaterial({
                vertexColors: true,
                transparent: true,
                opacity: 0.7 + (layerIndex / totalLayers) * 0.2, // 70%-90%透明度
                side: THREE.DoubleSide
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.z = layerIndex * layerThickness; // 无空隙堆叠
            mesh.name = `对流换热系数层${layerIndex + 1}`;
            meshArray.push(mesh);

            return meshArray;
        }

        // 3D对流换热系数动画循环
        function animate3DHeat() {
            if (!heat3DViewActive) return;

            heat3DAnimationId = requestAnimationFrame(animate3DHeat);

            // 检查容器大小变化并调整渲染器
            const container = document.getElementById('heat3DContainer');
            if (container && heat3DRenderer && heat3DCamera) {
                const containerRect = container.getBoundingClientRect();
                const currentWidth = heat3DRenderer.domElement.width;
                const currentHeight = heat3DRenderer.domElement.height;

                if (Math.abs(containerRect.width - currentWidth) > 1 || Math.abs(containerRect.height - currentHeight) > 1) {
                    heat3DCamera.aspect = containerRect.width / containerRect.height;
                    heat3DCamera.updateProjectionMatrix();
                    heat3DRenderer.setSize(containerRect.width, containerRect.height);
                }
            }

            if (heat3DControls) {
                heat3DControls.update();
            }

            if (heat3DRenderer && heat3DScene && heat3DCamera) {
                heat3DRenderer.render(heat3DScene, heat3DCamera);
            }
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            `;

            if (type === 'success') {
                notification.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
            } else if (type === 'info') {
                notification.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
            } else if (type === 'error') {
                notification.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
            }

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 误差分析函数
        function showErrorAnalysis(data) {
            console.log('🔍 开始误差分析...');
            console.log('📊 数据结构:', data);

            try {
                const experimental = data.initial_shape || [];
                const predicted = data.predicted_shape || [];

                console.log('🧪 实验数据长度:', experimental.length);
                console.log('🤖 预测数据长度:', predicted.length);

                if (experimental.length === 0 || predicted.length === 0) {
                    console.warn('缺少实验数据或预测数据，无法计算误差');
                    return;
                }

                // 检查数据格式
                console.log('🔍 实验数据前3个点:', experimental.slice(0, 3));
                console.log('🔍 预测数据前3个点:', predicted.slice(0, 3));

                // 计算误差指标
                const errors = [];
                const minLength = Math.min(experimental.length, predicted.length);

                for (let i = 0; i < minLength; i++) {
                    const expPoint = experimental[i];
                    const predPoint = predicted[i];

                    if (!expPoint || !predPoint || expPoint.length < 2 || predPoint.length < 2) {
                        console.warn(`数据点${i}格式错误:`, expPoint, predPoint);
                        continue;
                    }

                    // 计算欧几里得距离误差
                    const error = Math.sqrt(
                        Math.pow(expPoint[0] - predPoint[0], 2) +
                        Math.pow(expPoint[1] - predPoint[1], 2)
                    );
                    errors.push(error);
                }

                console.log('📈 误差数组长度:', errors.length);
                console.log('📈 前10个误差值:', errors.slice(0, 10));

                if (errors.length === 0) {
                    console.warn('没有有效的误差数据');
                    return;
                }

                // 计算统计指标
                const mae = errors.reduce((sum, err) => sum + err, 0) / errors.length;
                const rmse = Math.sqrt(errors.reduce((sum, err) => sum + err * err, 0) / errors.length);
                const maxError = Math.max(...errors);
                const r2 = calculateR2(experimental, predicted);

                console.log('📊 计算结果:', { mae, rmse, maxError, r2 });

                // 保存误差分析结果到localStorage
                const errorData = { mae, rmse, maxError, r2 };
                localStorage.setItem('errorAnalysisData', JSON.stringify(errorData));

                // 更新UI显示
                updateErrorDisplay(mae, rmse, maxError, r2);

                // 显示误差面板
                const errorPanel = document.getElementById('errorAnalysisPanel');
                if (errorPanel) {
                    errorPanel.style.display = 'block';
                }

            } catch (error) {
                console.error('计算误差指标时出错:', error);
            }
        }

        function calculateR2(experimental, predicted) {
            try {
                const minLength = Math.min(experimental.length, predicted.length);
                let sumSquaredResiduals = 0;
                let sumSquaredTotal = 0;

                // 计算实验数据的平均值
                let expMeanX = 0, expMeanY = 0;
                for (let i = 0; i < minLength; i++) {
                    expMeanX += experimental[i][0];
                    expMeanY += experimental[i][1];
                }
                expMeanX /= minLength;
                expMeanY /= minLength;

                // 计算R²
                for (let i = 0; i < minLength; i++) {
                    const expX = experimental[i][0];
                    const expY = experimental[i][1];
                    const predX = predicted[i][0];
                    const predY = predicted[i][1];

                    sumSquaredResiduals += Math.pow(expX - predX, 2) + Math.pow(expY - predY, 2);
                    sumSquaredTotal += Math.pow(expX - expMeanX, 2) + Math.pow(expY - expMeanY, 2);
                }

                return Math.max(0, 1 - (sumSquaredResiduals / sumSquaredTotal));
            } catch (error) {
                console.error('计算R²时出错:', error);
                return 0;
            }
        }

        function updateErrorDisplay(mae, rmse, maxError, r2) {
            // 更新数值，使用科学计数法显示小数值
            document.getElementById('maeValue').textContent = mae < 0.001 ? mae.toExponential(2) : mae.toFixed(4);
            document.getElementById('rmseValue').textContent = rmse < 0.001 ? rmse.toExponential(2) : rmse.toFixed(4);
            document.getElementById('maxErrorValue').textContent = maxError < 0.001 ? maxError.toExponential(2) : maxError.toFixed(4);
            document.getElementById('r2Value').textContent = r2.toFixed(6);

            // 设置颜色
            const maeElement = document.getElementById('maeValue');
            const rmseElement = document.getElementById('rmseValue');
            const maxErrorElement = document.getElementById('maxErrorValue');
            const r2Element = document.getElementById('r2Value');

            // MAE颜色判断（调整阈值适应小误差）
            if (mae < 0.0001) maeElement.className = 'error-value good';
            else if (mae < 0.001) maeElement.className = 'error-value warning';
            else maeElement.className = 'error-value error';

            // RMSE颜色判断（调整阈值适应小误差）
            if (rmse < 0.0001) rmseElement.className = 'error-value good';
            else if (rmse < 0.001) rmseElement.className = 'error-value warning';
            else rmseElement.className = 'error-value error';

            // 最大误差颜色判断（调整阈值适应小误差）
            if (maxError < 0.0001) maxErrorElement.className = 'error-value good';
            else if (maxError < 0.001) maxErrorElement.className = 'error-value warning';
            else maxErrorElement.className = 'error-value error';

            // R²颜色判断（更精确的阈值）
            if (r2 > 0.999) r2Element.className = 'error-value good';
            else if (r2 > 0.99) r2Element.className = 'error-value warning';
            else r2Element.className = 'error-value error';
        }

        // 页面加载时恢复误差分析显示
        function restoreErrorAnalysis() {
            try {
                const savedErrorData = localStorage.getItem('errorAnalysisData');
                if (savedErrorData) {
                    const errorData = JSON.parse(savedErrorData);
                    console.log('🔄 恢复误差分析数据:', errorData);

                    // 更新UI显示
                    updateErrorDisplay(errorData.mae, errorData.rmse, errorData.maxError, errorData.r2);

                    // 显示误差面板
                    const errorPanel = document.getElementById('errorAnalysisPanel');
                    if (errorPanel) {
                        errorPanel.style.display = 'block';
                    }
                }
            } catch (error) {
                console.error('恢复误差分析数据时出错:', error);
            }
        }

        // 清除误差分析数据
        function clearErrorAnalysis() {
            try {
                localStorage.removeItem('errorAnalysisData');
                const errorPanel = document.getElementById('errorAnalysisPanel');
                if (errorPanel) {
                    errorPanel.style.display = 'none';
                }
                console.log('🗑️ 已清除误差分析数据');
            } catch (error) {
                console.error('清除误差分析数据时出错:', error);
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟一点时间确保所有元素都已加载
            setTimeout(() => {
                restoreErrorAnalysis();
            }, 1000);
        });
    </script>
</body>
</html>
