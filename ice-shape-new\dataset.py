#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新版PhysEvolveNet数据集处理模块
处理冰形演化数据：输入冰形+工况+时间 -> 输出未来冰形
"""

import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset
from scipy.interpolate import splprep, splev, interp1d
import tqdm
import pickle


class TorchStandardScaler:
    """PyTorch版本的标准化器"""
    def __init__(self):
        self.mean_ = None
        self.std_ = None
        self.fitted = False

    def fit(self, X):
        """拟合标准化参数"""
        if isinstance(X, np.ndarray):
            X = torch.from_numpy(X).float()

        self.mean_ = torch.mean(X, dim=0)
        self.std_ = torch.std(X, dim=0)
        # 避免除零
        self.std_ = torch.where(self.std_ == 0, torch.ones_like(self.std_), self.std_)
        self.fitted = True
        return self

    def transform(self, X):
        """标准化变换"""
        if not self.fitted:
            raise ValueError("Scaler not fitted yet.")

        if isinstance(X, np.ndarray):
            X = torch.from_numpy(X).float()

        X_scaled = (X - self.mean_) / self.std_
        return X_scaled.numpy()

    def inverse_transform(self, X):
        """反标准化变换"""
        if not self.fitted:
            raise ValueError("Scaler not fitted yet.")

        if isinstance(X, np.ndarray):
            X = torch.from_numpy(X).float()

        X_original = X * self.std_ + self.mean_
        return X_original.numpy()


class TorchMinMaxScaler:
    """PyTorch版本的最小-最大归一化器"""
    def __init__(self, feature_range=(0, 1)):
        self.feature_range = feature_range
        self.data_min_ = None
        self.data_max_ = None
        self.data_range_ = None
        self.fitted = False

    def fit(self, X):
        """拟合归一化参数"""
        if isinstance(X, np.ndarray):
            X = torch.from_numpy(X).float()

        self.data_min_ = torch.min(X, dim=0)[0]
        self.data_max_ = torch.max(X, dim=0)[0]
        self.data_range_ = self.data_max_ - self.data_min_
        # 避免除零
        self.data_range_ = torch.where(self.data_range_ == 0, torch.ones_like(self.data_range_), self.data_range_)
        self.fitted = True
        return self

    def transform(self, X):
        """归一化变换"""
        if not self.fitted:
            raise ValueError("Scaler not fitted yet.")

        if isinstance(X, np.ndarray):
            X = torch.from_numpy(X).float()

        X_std = (X - self.data_min_) / self.data_range_
        X_scaled = X_std * (self.feature_range[1] - self.feature_range[0]) + self.feature_range[0]
        return X_scaled.numpy()

    def inverse_transform(self, X):
        """反归一化变换"""
        if not self.fitted:
            raise ValueError("Scaler not fitted yet.")

        if isinstance(X, np.ndarray):
            X = torch.from_numpy(X).float()

        X_std = (X - self.feature_range[0]) / (self.feature_range[1] - self.feature_range[0])
        X_original = X_std * self.data_range_ + self.data_min_
        return X_original.numpy()


class IceShapeDataset(Dataset):
    """
    冰形演化数据集

    数据结构：
    - result-all/NACA0012_X/result/T/beta_0.dat (X为工况索引，T为时间步)
    - Cases.txt 包含工况信息：H(m), V(m/s), T, LWC, MVD, AOA

    输入：初始冰形 + 工况 + 目标时间
    输出：目标时间的冰形
    """

    def __init__(self, data_root, cases_file, num_points=200, max_time_steps=10,
                 normalize=True, scaler_save_path=None):
        """
        Args:
            data_root: result-all文件夹路径
            cases_file: Cases.txt文件路径
            num_points: 重采样后的点数
            max_time_steps: 最大时间步数
            normalize: 是否进行数据归一化
            scaler_save_path: 归一化器保存路径
        """
        self.data_root = data_root
        self.num_points = num_points
        self.max_time_steps = max_time_steps
        self.normalize = normalize
        self.scaler_save_path = scaler_save_path

        # 加载工况数据
        self.cases_df = pd.read_csv(cases_file, sep='\t')
        print(f"加载了 {len(self.cases_df)} 个工况")

        # 初始化归一化器
        self.coord_scaler = TorchStandardScaler()  # 坐标使用标准化
        self.condition_scaler = TorchStandardScaler()  # 工况使用标准化
        self.time_scaler = TorchMinMaxScaler()  # 时间使用最小-最大归一化

        # 构建数据索引
        self.data_samples = []
        self._build_dataset_index()

        # 计算归一化参数
        if self.normalize:
            self._compute_normalization_params()

        print(f"构建了 {len(self.data_samples)} 个训练样本")
    
    def _build_dataset_index(self):
        """构建数据集索引"""
        case_folders = [f for f in os.listdir(self.data_root) if f.startswith('NACA0012_')]
        
        for case_folder in tqdm.tqdm(case_folders, desc="构建数据索引"):
            # 提取工况索引
            case_idx = int(case_folder.split('_')[1]) - 1  # NACA0012_1 -> index 0
            
            if case_idx >= len(self.cases_df):
                continue
                
            case_path = os.path.join(self.data_root, case_folder, 'result')
            
            if not os.path.exists(case_path):
                continue
            
            # 获取所有时间步文件夹
            time_folders = [f for f in os.listdir(case_path) if f.isdigit()]
            time_folders = sorted([int(f) for f in time_folders])
            
            # 为每个时间步创建训练样本
            for t_start in range(len(time_folders) - 1):  # 不包括最后一个时间步
                for t_target in range(t_start + 1, min(t_start + self.max_time_steps, len(time_folders))):
                    start_file = os.path.join(case_path, str(time_folders[t_start]), 'beta_0.dat')
                    target_file = os.path.join(case_path, str(time_folders[t_target]), 'beta_0.dat')
                    
                    if os.path.exists(start_file) and os.path.exists(target_file):
                        # 文件夹序号0,1,2,...,9对应时间1,2,3,...,10分钟
                        start_time_min = time_folders[t_start] + 1
                        target_time_min = time_folders[t_target] + 1

                        self.data_samples.append({
                            'case_idx': case_idx,
                            'start_time': time_folders[t_start],  # 文件夹序号
                            'target_time': time_folders[t_target],  # 文件夹序号
                            'start_time_min': start_time_min,  # 实际时间（分钟）
                            'target_time_min': target_time_min,  # 实际时间（分钟）
                            'start_file': start_file,
                            'target_file': target_file,
                            'time_delta': target_time_min - start_time_min  # 真实时间差
                        })

    def _compute_normalization_params(self):
        """计算归一化参数"""
        print("计算归一化参数...")

        # 收集所有坐标数据
        all_coords = []
        all_conditions = []
        all_time_deltas = []

        # 采样部分数据来计算统计量（避免内存不足）
        sample_indices = np.random.choice(len(self.data_samples),
                                        min(1000, len(self.data_samples)),
                                        replace=False)

        for idx in sample_indices:
            sample = self.data_samples[idx]

            # 加载冰形数据
            start_shape = self._load_ice_shape_raw(sample['start_file'])
            target_shape = self._load_ice_shape_raw(sample['target_file'])

            if start_shape is not None and target_shape is not None:
                all_coords.extend([start_shape, target_shape])

                # 工况数据
                case_conditions = self.cases_df.iloc[sample['case_idx']].values.astype(np.float32)
                all_conditions.append(case_conditions)

                # 时间差
                all_time_deltas.append(sample['time_delta'])

        if all_coords:
            # 拟合坐标归一化器
            coords_array = np.vstack(all_coords)
            self.coord_scaler.fit(coords_array)

            # 拟合工况归一化器
            conditions_array = np.array(all_conditions)
            self.condition_scaler.fit(conditions_array)

            # 拟合时间归一化器
            time_array = np.array(all_time_deltas).reshape(-1, 1)
            self.time_scaler.fit(time_array)

            print(f"坐标统计: mean={self.coord_scaler.mean_.numpy()}, std={self.coord_scaler.std_.numpy()}")
            print(f"工况统计: mean={self.condition_scaler.mean_.numpy()}, std={self.condition_scaler.std_.numpy()}")
            print(f"时间范围: [{self.time_scaler.data_min_.numpy()[0]:.1f}, {self.time_scaler.data_max_.numpy()[0]:.1f}]")

            # 保存归一化器
            if self.scaler_save_path:
                self.save_scalers(self.scaler_save_path)

    def save_scalers(self, filepath):
        """保存归一化器"""
        scalers = {
            'coord_scaler': self.coord_scaler,
            'condition_scaler': self.condition_scaler,
            'time_scaler': self.time_scaler
        }
        with open(filepath, 'wb') as f:
            pickle.dump(scalers, f)
        print(f"归一化器保存到: {filepath}")

    def load_scalers(self, filepath):
        """加载归一化器"""
        with open(filepath, 'rb') as f:
            scalers = pickle.load(f)
        self.coord_scaler = scalers['coord_scaler']
        self.condition_scaler = scalers['condition_scaler']
        self.time_scaler = scalers['time_scaler']
        print(f"归一化器加载自: {filepath}")

    def _load_ice_shape_raw(self, filepath):
        """加载原始冰形数据（不重采样）"""
        try:
            data = np.loadtxt(filepath)
            if data.ndim == 2 and data.shape[1] >= 3:
                return data[:, :3]
            return None
        except:
            return None

    def _load_ice_shape(self, filepath):
        """加载冰形数据"""
        try:
            data = np.loadtxt(filepath)
            if data.ndim == 2 and data.shape[1] >= 3:
                # 取前3列作为x,y,z坐标
                ice_shape = data[:, :3]
                return self._resample_ice_shape(ice_shape)
            return None
        except:
            return None
    
    def _resample_ice_shape(self, points_3d):
        """重采样冰形到固定点数"""
        if points_3d is None or len(points_3d) < 4:
            return None
            
        try:
            x, y, z = points_3d[:, 0], points_3d[:, 1], points_3d[:, 2]
            
            # 使用样条插值重采样x,y坐标
            tck, u = splprep([x, y], s=0, per=True, quiet=True)
            
            # 对z坐标进行线性插值
            z_interpolator = interp1d(u, z, kind='linear', fill_value="extrapolate")
            
            # 生成新的参数点
            new_u = np.linspace(u.min(), u.max(), self.num_points)
            
            # 插值得到新的x,y坐标
            new_xy = np.column_stack(splev(new_u, tck))
            
            # 插值得到新的z坐标
            new_z = z_interpolator(new_u).reshape(-1, 1)
            
            return np.hstack([new_xy, new_z])
        except:
            return None
    
    def __len__(self):
        return len(self.data_samples)
    
    def __getitem__(self, idx):
        sample = self.data_samples[idx]

        # 加载冰形数据
        start_shape = self._load_ice_shape(sample['start_file'])
        target_shape = self._load_ice_shape(sample['target_file'])

        if start_shape is None or target_shape is None:
            # 返回零数据作为占位符
            start_shape = np.zeros((self.num_points, 3))
            target_shape = np.zeros((self.num_points, 3))

        # 获取工况数据
        case_conditions = self.cases_df.iloc[sample['case_idx']].values.astype(np.float32)
        time_delta = np.array([sample['time_delta']], dtype=np.float32)

        # 应用归一化
        if self.normalize:
            start_shape = self.coord_scaler.transform(start_shape)
            target_shape = self.coord_scaler.transform(target_shape)
            case_conditions = self.condition_scaler.transform(case_conditions.reshape(1, -1)).flatten()
            time_delta = self.time_scaler.transform(time_delta.reshape(-1, 1)).flatten()

        # 构建输入特征：冰形(x,y,z) + 工况(6维) + 时间差(1维)
        # 将工况和时间差广播到每个点
        conditions_expanded = np.tile(case_conditions, (self.num_points, 1))  # [N, 6]
        time_delta_expanded = np.full((self.num_points, 1), time_delta[0], dtype=np.float32)  # [N, 1]

        # 拼接特征：[N, 3+6+1] = [N, 10]
        input_features = np.hstack([start_shape, conditions_expanded, time_delta_expanded])

        # 目标只需要x,y坐标（前2列）
        target_xy = target_shape[:, :2]  # [N, 2] 只取x,y坐标

        # 加载原始数据用于反归一化（处理可能的None值）
        original_start = self._load_ice_shape(sample['start_file'])
        original_target = self._load_ice_shape(sample['target_file'])

        # 如果加载失败，使用当前处理后的数据
        if original_start is None:
            original_start = np.column_stack([start_shape[:, :2], np.zeros((start_shape.shape[0], 1))])
        if original_target is None:
            original_target = np.column_stack([target_xy, np.zeros((target_xy.shape[0], 1))])

        return {
            'input_features': torch.FloatTensor(input_features),  # [N, 10] 输入包含x,y,z+工况+时间
            'target_shape': torch.FloatTensor(target_xy),         # [N, 2] 目标只有x,y
            'case_idx': sample['case_idx'],
            'start_time': sample['start_time'],
            'target_time': sample['target_time'],
            'time_delta': sample['time_delta'],
            # 保存原始数据用于反归一化
            'original_start_shape': torch.FloatTensor(original_start),
            'original_target_shape': torch.FloatTensor(original_target)
        }

    def denormalize_coords(self, normalized_coords):
        """反归一化坐标"""
        if self.normalize:
            return self.coord_scaler.inverse_transform(normalized_coords)
        return normalized_coords

    def denormalize_conditions(self, normalized_conditions):
        """反归一化工况"""
        if self.normalize:
            return self.condition_scaler.inverse_transform(normalized_conditions)
        return normalized_conditions

    def denormalize_time(self, normalized_time):
        """反归一化时间"""
        if self.normalize:
            return self.time_scaler.inverse_transform(normalized_time)
        return normalized_time


def create_dataloaders(data_root, cases_file, batch_size=16, train_ratio=0.8, num_workers=4,
                      normalize=True, scaler_save_path="scalers.pkl"):
    """创建训练和验证数据加载器"""

    # 创建完整数据集
    full_dataset = IceShapeDataset(
        data_root, cases_file,
        normalize=normalize,
        scaler_save_path=scaler_save_path
    )

    # 划分训练和验证集
    total_size = len(full_dataset)
    train_size = int(total_size * train_ratio)
    val_size = total_size - train_size

    train_dataset, val_dataset = torch.utils.data.random_split(
        full_dataset, [train_size, val_size]
    )

    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )

    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )

    return train_loader, val_loader, full_dataset


if __name__ == "__main__":
    # 测试数据集
    data_root = "result-all"
    cases_file = "Cases.txt"
    
    dataset = IceShapeDataset(data_root, cases_file)
    
    print(f"数据集大小: {len(dataset)}")
    
    # 测试一个样本
    sample = dataset[0]
    print(f"输入特征形状: {sample['input_features'].shape}")
    print(f"目标冰形形状: {sample['target_shape'].shape}")
    print(f"工况索引: {sample['case_idx']}")
    print(f"时间差: {sample['time_delta']}")
