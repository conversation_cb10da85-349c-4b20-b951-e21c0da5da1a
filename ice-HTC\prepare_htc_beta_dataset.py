#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTC和Beta预测数据集准备脚本
输入：xyz坐标 + 工况参数 + 时间
输出：对流换热系数(HTC) + 水滴收集系数(beta)
"""

import os
import glob
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset
import json
from tqdm import tqdm

class HTCBetaDataset(Dataset):
    """
    HTC和Beta预测数据集
    """
    
    def __init__(self, result_all_dir, cases_txt_path, stats=None, train_mode=True):
        """
        Args:
            result_all_dir: result-all文件夹路径
            cases_txt_path: Cases_processed_fixed.txt文件路径
            stats: 归一化统计数据
            train_mode: 是否为训练模式
        """
        self.result_all_dir = result_all_dir
        self.cases_txt_path = cases_txt_path
        self.train_mode = train_mode
        
        print("正在初始化HTC-Beta数据集...")
        
        # 加载工况数据
        self.cases_df = pd.read_csv(cases_txt_path, sep='\t')
        print(f"加载了 {len(self.cases_df)} 个工况")
        
        # 设置归一化统计数据
        if stats is None:
            self.stats = self._compute_normalization_stats()
        else:
            self.stats = stats
            
        # 加载所有数据点
        self.data_points = self._load_all_data_points()
        print(f"数据集初始化完成，共 {len(self.data_points)} 个数据点")
    
    def _compute_normalization_stats(self):
        """计算归一化统计数据"""
        print("正在计算归一化统计数据...")
        
        stats = {
            'xyz': {'min': np.array([float('inf')] * 3), 'max': np.array([float('-inf')] * 3)},
            'conditions': {'min': np.array([float('inf')] * 5), 'max': np.array([float('-inf')] * 5)},
            'htc': {'min': float('inf'), 'max': float('-inf')},
            'beta': {'min': float('inf'), 'max': float('-inf')}
        }
        
        # 从工况数据中获取条件范围
        velocity_range = [self.cases_df['Velocity'].min(), self.cases_df['Velocity'].max()]
        temp_range = [self.cases_df['Temperature'].min(), self.cases_df['Temperature'].max()]
        lwc_range = [self.cases_df['LWC'].min(), self.cases_df['LWC'].max()]
        mvd_range = [self.cases_df['MVD'].min(), self.cases_df['MVD'].max()]
        time_range = [0.0, 9.0]  # 时间从0到9（对应1-10分钟）
        
        stats['conditions']['min'] = np.array([velocity_range[0], temp_range[0], lwc_range[0], mvd_range[0], time_range[0]])
        stats['conditions']['max'] = np.array([velocity_range[1], temp_range[1], lwc_range[1], mvd_range[1], time_range[1]])
        
        # 采样一些数据点来计算xyz、htc、beta的范围
        sample_folders = glob.glob(os.path.join(self.result_all_dir, "NACA0012_*"))[:50]  # 采样50个文件夹
        
        for folder in tqdm(sample_folders, desc="采样计算统计数据"):
            folder_name = os.path.basename(folder)
            if not folder_name.startswith("NACA0012_"):
                continue
                
            for time_step in range(10):  # 0-9时间步
                result_folder = os.path.join(folder, "result", str(time_step))
                dat_file = os.path.join(result_folder, "beta_0.dat")
                
                if os.path.exists(dat_file):
                    try:
                        data = np.loadtxt(dat_file)
                        if data.ndim == 2 and data.shape[1] >= 5:
                            xyz = data[:, :3]  # x, y, z
                            beta = data[:, 3]   # beta值
                            htc = data[:, 4]    # HTC值
                            
                            # 更新xyz范围
                            stats['xyz']['min'] = np.minimum(stats['xyz']['min'], xyz.min(axis=0))
                            stats['xyz']['max'] = np.maximum(stats['xyz']['max'], xyz.max(axis=0))
                            
                            # 更新htc范围
                            stats['htc']['min'] = min(stats['htc']['min'], htc.min())
                            stats['htc']['max'] = max(stats['htc']['max'], htc.max())
                            
                            # 更新beta范围
                            stats['beta']['min'] = min(stats['beta']['min'], beta.min())
                            stats['beta']['max'] = max(stats['beta']['max'], beta.max())
                            
                    except Exception as e:
                        continue
        
        print("归一化统计数据计算完成")
        print(f"XYZ范围: {stats['xyz']['min']} ~ {stats['xyz']['max']}")
        print(f"条件范围: {stats['conditions']['min']} ~ {stats['conditions']['max']}")
        print(f"HTC范围: {stats['htc']['min']} ~ {stats['htc']['max']}")
        print(f"Beta范围: {stats['beta']['min']} ~ {stats['beta']['max']}")
        
        return stats
    
    def _load_all_data_points(self):
        """加载所有数据点"""
        print("正在加载所有数据点...")
        data_points = []
        
        for idx, row in tqdm(self.cases_df.iterrows(), total=len(self.cases_df), desc="加载数据"):
            folder_name = row['FolderName']
            folder_path = os.path.join(self.result_all_dir, folder_name)
            
            if not os.path.exists(folder_path):
                continue
                
            # 工况参数
            velocity = row['Velocity']
            temperature = row['Temperature']
            lwc = row['LWC']
            mvd = row['MVD']
            
            # 遍历所有时间步
            for time_step in range(10):  # 0-9对应1-10分钟
                result_folder = os.path.join(folder_path, "result", str(time_step))
                dat_file = os.path.join(result_folder, "beta_0.dat")
                
                if os.path.exists(dat_file):
                    try:
                        data = np.loadtxt(dat_file)
                        if data.ndim == 2 and data.shape[1] >= 5:
                            xyz = data[:, :3]  # x, y, z坐标
                            beta = data[:, 3]   # beta值
                            htc = data[:, 4]    # HTC值
                            
                            # 为每个点创建数据条目
                            for point_idx in range(len(xyz)):
                                data_point = {
                                    'xyz': xyz[point_idx],
                                    'conditions': np.array([velocity, temperature, lwc, mvd, float(time_step)]),
                                    'htc': htc[point_idx],
                                    'beta': beta[point_idx],
                                    'case_idx': idx,
                                    'time_step': time_step,
                                    'point_idx': point_idx
                                }
                                data_points.append(data_point)
                                
                    except Exception as e:
                        continue
        
        return data_points
    
    def _normalize_data(self, data, data_type):
        """归一化数据"""
        if data_type in ['xyz', 'conditions']:
            min_val = self.stats[data_type]['min']
            max_val = self.stats[data_type]['max']
            return (data - min_val) / (max_val - min_val + 1e-8)
        else:  # htc, beta
            min_val = self.stats[data_type]['min']
            max_val = self.stats[data_type]['max']
            return (data - min_val) / (max_val - min_val + 1e-8)
    
    def _denormalize_data(self, norm_data, data_type):
        """反归一化数据"""
        if data_type in ['xyz', 'conditions']:
            min_val = self.stats[data_type]['min']
            max_val = self.stats[data_type]['max']
            return norm_data * (max_val - min_val) + min_val
        else:  # htc, beta
            min_val = self.stats[data_type]['min']
            max_val = self.stats[data_type]['max']
            return norm_data * (max_val - min_val) + min_val
    
    def get_stats(self):
        """获取归一化统计数据"""
        return self.stats
    
    def __len__(self):
        return len(self.data_points)
    
    def __getitem__(self, idx):
        data_point = self.data_points[idx]
        
        # 归一化输入数据
        xyz_norm = self._normalize_data(data_point['xyz'], 'xyz')
        conditions_norm = self._normalize_data(data_point['conditions'], 'conditions')
        
        # 归一化目标数据
        htc_norm = self._normalize_data(data_point['htc'], 'htc')
        beta_norm = self._normalize_data(data_point['beta'], 'beta')
        
        return {
            'xyz': torch.tensor(xyz_norm, dtype=torch.float32),
            'conditions': torch.tensor(conditions_norm, dtype=torch.float32),
            'htc': torch.tensor(htc_norm, dtype=torch.float32),
            'beta': torch.tensor(beta_norm, dtype=torch.float32),
            'case_idx': data_point['case_idx'],
            'time_step': data_point['time_step'],
            'point_idx': data_point['point_idx']
        }

def save_stats(stats, filepath):
    """保存统计数据到文件"""
    # 转换numpy数组为列表以便JSON序列化
    stats_serializable = {}
    for key, value in stats.items():
        if isinstance(value, dict):
            stats_serializable[key] = {}
            for sub_key, sub_value in value.items():
                if isinstance(sub_value, np.ndarray):
                    stats_serializable[key][sub_key] = sub_value.tolist()
                else:
                    stats_serializable[key][sub_key] = sub_value
        else:
            stats_serializable[key] = value
    
    with open(filepath, 'w') as f:
        json.dump(stats_serializable, f, indent=2)
    print(f"统计数据已保存到: {filepath}")

def load_stats(filepath):
    """从文件加载统计数据"""
    with open(filepath, 'r') as f:
        stats_dict = json.load(f)
    
    # 转换列表回numpy数组
    for key, value in stats_dict.items():
        if isinstance(value, dict):
            for sub_key, sub_value in value.items():
                if isinstance(sub_value, list):
                    stats_dict[key][sub_key] = np.array(sub_value)
    
    print(f"统计数据已从 {filepath} 加载")
    return stats_dict

if __name__ == "__main__":
    # 测试数据集
    result_all_dir = "../result-all"
    cases_txt_path = "../Cases_processed_fixed.txt"
    
    # 创建数据集
    dataset = HTCBetaDataset(result_all_dir, cases_txt_path)
    
    # 保存统计数据
    save_stats(dataset.get_stats(), "htc_beta_stats.json")
    
    print(f"数据集大小: {len(dataset)}")
    
    # 测试一个样本
    sample = dataset[0]
    print("样本数据:")
    for key, value in sample.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.shape} - {value}")
        else:
            print(f"{key}: {value}")
