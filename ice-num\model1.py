import torch
import torch.nn as nn
from point_transformer import PointTransformer
from EnhancedFiLM import EnhancedFiLM  # ✅ 使用增强版 FiLM

class PointTransformerWithFiLM(nn.Module):
    """
    结合 PointTransformer + EnhancedFiLM 进行点云特征提取和气动系数预测
    """
    def __init__(self, input_dim=3, hidden_dim=64, num_heads=4, dropout=0.3):
        super().__init__()
        # ✅ PointTransformer 提取点云特征
        self.point_transformer = PointTransformer(input_dim=input_dim, hidden_dim=hidden_dim, num_heads=num_heads)

        # ✅ 使用 EnhancedFiLM（非线性环境调制）
        self.film = EnhancedFiLM(hidden_dim)

        self.dropout = nn.Dropout(dropout)  # ✅ 适当增加 Dropout，防止过拟合

        # ✅ MLP 预测气动系数
        self.mlp_head = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 3)  # 预测 (C_L, C_D, C_M)
        )

    def forward(self, points, env_params):
        """
        :param points: (batch_size, num_points, input_dim)  点云数据 (x, y, z)
        :param env_params: (batch_size, 4)  物理环境参数 (LWC, MVD, Temperature, AOA)
        :return: 预测的气动系数 (batch_size, 3)
        """
        # ✅ 通过 PointTransformer 提取点云特征
        x = self.point_transformer(points)

        # ✅ 用 EnhancedFiLM 让环境参数调制 Transformer 输出
        x = self.film(x, env_params)

        # ✅ Dropout 防止过拟合
        x = self.dropout(x)

        # ✅ 全局池化（mean pooling），转换为固定大小的向量
        x = x.mean(dim=1)

        # ✅ MLP 预测气动系数 (C_L, C_D, C_M)
        output = self.mlp_head(x)

        return output


# ✅ 运行测试
if __name__ == "__main__":
    batch_size = 4
    num_points = 519
    input_dim = 3  # (x, y, z)
    env_dim = 4  # (LWC, MVD, Temperature, AOA)

    # 生成测试数据
    test_points = torch.randn(batch_size, num_points, input_dim)
    test_env_params = torch.randn(batch_size, env_dim)

    # ✅ 初始化模型（增加 Dropout）
    model = PointTransformerWithFiLM(input_dim=input_dim, hidden_dim=64, num_heads=4, dropout=0.3)

    # ✅ 运行模型
    output = model(test_points, test_env_params)

    # ✅ 打印输出形状
    print("输入点云形状:", test_points.shape)  # (4, 519, 3)
    print("环境参数形状:", test_env_params.shape)  # (4, 4)
    print("输出气动系数形状:", output.shape)  # (4, 3)
