<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NACA0012翼型 - AI智能结冰预测时序分析</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom"></script>
    <!-- Three.js 和相关库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .conditions {
            width: calc(90% - 50px);
            margin: 20px auto;
            padding: 25px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            display: flex;
            justify-content: space-around;
            align-items: center;
            transition: all 0.3s ease;
        }
        .conditions:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
        }
        .condition-item {
            text-align: center;
            font-size: 1.2em;
            color: #4a5568;
            font-weight: 600;
        }
        .condition-item p:first-child {
            margin-bottom: 8px;
            font-size: 0.9em;
            color: #667eea;
            font-weight: 700;
        }
        .condition-item p:last-child {
            margin: 0;
            font-size: 1.1em;
            color: #2d3748;
        }
        .chart-row {
            display: flex;
            justify-content: space-between;
            width: 90%;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .chart-container {
            flex: 1;
            margin: 10px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .chart-container:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(31, 38, 135, 0.5);
        }
        
        .chart-container.selected {
            border: 3px solid #e74c3c !important;
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(231, 76, 60, 0.3);
        }
        .chart-container h3 {
            margin-bottom: 15px;
            font-size: 20px;
            color: #2d3748;
            font-weight: 600;
            text-align: center;
        }
        canvas {
            max-width: 100%;
            height: 400px;
        }
        .back-button {
            margin: 20px;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
        .back-button:active {
            transform: translateY(0);
        }
        footer {
            text-align: center;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 0.9em;
            color: #4a5568;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 0;
            width: 100%;
        }
        
        /* 页面标题样式 */
        .page-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            padding: 25px;
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            width: calc(90% - 50px);
            margin: 20px auto;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
        }
        
        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .chart-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        .chart-container:nth-child(1) { animation-delay: 0.1s; }
        .chart-container:nth-child(2) { animation-delay: 0.2s; }
        .chart-container:nth-child(3) { animation-delay: 0.3s; }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .chart-row {
                flex-direction: column;
            }
            .conditions {
                flex-direction: column;
                gap: 20px;
            }
            .condition-item {
                width: 100%;
            }
        }
        
        /* 3D冰形容器样式 */
        .ice3d-container {
            width: 90%;
            height: 400px;
            margin: 20px auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .ice3d-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(31, 38, 135, 0.5);
        }
        
        .ice3d-container h3 {
            margin: 0;
            padding: 20px;
            font-size: 20px;
            color: #333333;
            font-weight: 600;
            text-align: center;
            background: none;
            border-radius: 0;
        }
        
        .ice3d-canvas {
            width: 100%;
            height: calc(100% - 60px);
            position: relative;
        }
        
        /* 3D加载状态 */
        .ice3d-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 100;
        }
        
        .ice3d-loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .ice3d-loading-text {
            color: #667eea;
            font-size: 14px;
            font-weight: 600;
        }
        
        /* 3D图例 */
        .ice3d-legend {
            position: absolute;
            bottom: 15px;
            left: 15px;
            background: rgba(255,255,255,0.9);
            padding: 12px;
            border-radius: 10px;
            font-size: 12px;
            z-index: 50;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="page-title">
        🔬 NACA0012翼型 - AI智能结冰预测时序分析
    </div>

    <!-- 工况信息展示 -->
    <div class="conditions">
        <div class="condition-item">
            <p>🚀 速度</p>
            <p><span id="velocity"></span> m/s</p>
        </div>
        <div class="condition-item">
            <p>🌡️ 温度</p>
            <p><span id="temperature"></span> K</p>
        </div>
        <div class="condition-item">
            <p>💧 液态水含量</p>
            <p><span id="lwc"></span> g/m³</p>
        </div>
        <div class="condition-item">
            <p>🔬 中值直径</p>
            <p><span id="mvd"></span> μm</p>
        </div>
        <div class="condition-item">
            <p>📏 高度</p>
            <p><span id="height"></span> m</p>
        </div>
        <div class="condition-item">
            <p>📐 攻角</p>
            <p><span id="aoa"></span> °</p>
        </div>
    </div>

        <!-- 3D冰形可视化 -->
        <div class="ice3d-container">
            <h3>3D时序冰形可视化 - 6个时间点演化</h3>
            <div class="ice3d-canvas" id="ice3DCanvas">
                <!-- 3D加载状态指示器 -->
                <div class="ice3d-loading" id="ice3DLoading">
                    <div class="ice3d-loading-spinner"></div>
                    <div class="ice3d-loading-text" id="ice3DLoadingText">正在初始化3D冰形场景...</div>
                </div>
                
                <!-- 3D控制按钮 -->
                <div style="position: absolute; top: 15px; right: 15px; z-index: 100;">
                    <button id="showAllTimesBtn" style="
                        background: rgba(102, 126, 234, 0.9);
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                        font-weight: 600;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(90, 103, 216, 0.9)'" 
                       onmouseout="this.style.background='rgba(102, 126, 234, 0.9)'">
                        📊 显示全部时间点
                    </button>
                </div>            <!-- 3D图例 -->
            <div class="ice3d-legend" id="ice3DLegend" style="display: none;">
                <div style="margin-bottom: 8px; font-weight: bold; color: #333;">📊 3D时序冰形图例</div>
                <div style="display: flex; align-items: center; margin-bottom: 6px;">
                    <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(31, 78, 121, 0.4), rgba(31, 78, 121, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                    <span>🔵 深蓝色 - 干净机翼(底层)</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(231, 76, 60, 0.4), rgba(231, 76, 60, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                    <span>🔴 T+1分钟 预测冰形</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(211, 47, 47, 0.4), rgba(211, 47, 47, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                    <span>🔴 T+2分钟 预测冰形</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(198, 40, 40, 0.4), rgba(198, 40, 40, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                    <span>� T+3分钟 预测冰形</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(183, 28, 28, 0.4), rgba(183, 28, 28, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                    <span>🔴 T+4分钟 预测冰形</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(142, 36, 170, 0.4), rgba(142, 36, 170, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                    <span>🟣 T+5分钟 预测冰形</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 6px;">
                    <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(106, 27, 154, 0.4), rgba(106, 27, 154, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                    <span>� T+6分钟 预测冰形</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(76, 175, 80, 0.4), rgba(76, 175, 80, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                    <span>🟢 T+7分钟 预测冰形</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(56, 142, 60, 0.4), rgba(56, 142, 60, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                    <span>🟢 T+8分钟 预测冰形</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <div style="width: 25px; height: 6px; background: linear-gradient(90deg, rgba(255, 152, 0, 0.4), rgba(255, 152, 0, 0.8)); margin-right: 10px; border-radius: 2px;"></div>
                    <span>🟠 T+9分钟 预测冰形</span>
                </div>

                <div style="display: flex; align-items: center; font-size: 11px; color: #666; margin-top: 6px;">
                    <span>⚡ Z轴方向显示时间演化，颜色渐变表示不同时刻</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表布局调整为每排三个图 -->
    <div class="chart-row">
        <div class="chart-container" data-time="1" style="cursor: pointer;">
            <h3>⏱️ T+1分钟 预测冰形</h3>
            <canvas id="chart1min"></canvas>
        </div>
        <div class="chart-container" data-time="2" style="cursor: pointer;">
            <h3>⏱️ T+2分钟 预测冰形</h3>
            <canvas id="chart2min"></canvas>
        </div>
        <div class="chart-container" data-time="3" style="cursor: pointer;">
            <h3>⏱️ T+3分钟 预测冰形</h3>
            <canvas id="chart3min"></canvas>
        </div>
    </div>
    <div class="chart-row">
        <div class="chart-container" data-time="4" style="cursor: pointer;">
            <h3>⏱️ T+4分钟 预测冰形</h3>
            <canvas id="chart4min"></canvas>
        </div>
        <div class="chart-container" data-time="5" style="cursor: pointer;">
            <h3>⏱️ T+5分钟 预测冰形</h3>
            <canvas id="chart5min"></canvas>
        </div>
        <div class="chart-container" data-time="6" style="cursor: pointer;">
            <h3>⏱️ T+6分钟 预测冰形</h3>
            <canvas id="chart6min"></canvas>
        </div>
    </div>
    <div class="chart-row">
        <div class="chart-container" data-time="7" style="cursor: pointer;">
            <h3>⏱️ T+7分钟 预测冰形</h3>
            <canvas id="chart7min"></canvas>
        </div>
        <div class="chart-container" data-time="8" style="cursor: pointer;">
            <h3>⏱️ T+8分钟 预测冰形</h3>
            <canvas id="chart8min"></canvas>
        </div>
        <div class="chart-container" data-time="9" style="cursor: pointer;">
            <h3>⏱️ T+9分钟 预测冰形</h3>
            <canvas id="chart9min"></canvas>
        </div>
    </div>


    <button class="back-button" onclick="history.back()">
        <span style="margin-right: 8px;">⬅️</span>返回主界面
    </button>

    <script>
        // 3D冰形可视化相关全局变量
        let ice3DScene = null;
        let ice3DCamera = null;
        let ice3DRenderer = null;
        let ice3DControls = null;
        let ice3DMeshes = [];
        let ice3DAnimationId = null;
        
        // 3D冰形加载状态管理
        function show3DIceLoading(message = '正在初始化3D冰形场景...') {
            const loadingElement = document.getElementById('ice3DLoading');
            const loadingText = document.getElementById('ice3DLoadingText');
            
            if (loadingElement) {
                loadingElement.style.display = 'flex';
                if (loadingText) {
                    loadingText.textContent = message;
                }
                console.log('显示3D冰形加载状态:', message);
            }
        }
        
        function hide3DIceLoading() {
            const loadingElement = document.getElementById('ice3DLoading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
                console.log('隐藏3D冰形加载状态');
            }
        }
        
        // 初始化3D冰形可视化
        function init3DIceVisualization() {
            const container = document.getElementById('ice3DCanvas');
            if (!container) {
                console.error('找不到ice3DCanvas容器');
                return;
            }
            
            console.log('开始初始化3D冰形场景...');
            show3DIceLoading('正在检查依赖...');
            
            // 检查Three.js是否可用
            if (typeof THREE === 'undefined') {
                console.error('Three.js未加载，无法初始化3D场景');
                hide3DIceLoading();
                return;
            }
            
            show3DIceLoading('正在创建3D场景...');
            
            // 创建Three.js场景
            ice3DScene = new THREE.Scene();
            ice3DScene.background = new THREE.Color(0xffffff);  // 纯白色背景
            
            // 创建相机
            const containerRect = container.getBoundingClientRect();
            ice3DCamera = new THREE.PerspectiveCamera(
                75, 
                containerRect.width / containerRect.height, 
                0.1, 
                1000
            );
            ice3DCamera.position.set(-0.86, 1.12, -0.79);  // 用户指定的理想观察角度
            
            show3DIceLoading('正在初始化渲染器...');
            
            // 创建渲染器
            ice3DRenderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            ice3DRenderer.setSize(containerRect.width, containerRect.height);
            ice3DRenderer.shadowMap.enabled = true;
            ice3DRenderer.shadowMap.type = THREE.PCFSoftShadowMap;
            
            // 将渲染器添加到容器
            container.appendChild(ice3DRenderer.domElement);
            console.log('3D渲染器canvas已添加');
            
            show3DIceLoading('正在设置控制器...');
            
            // 检查OrbitControls是否可用
            if (typeof THREE.OrbitControls !== 'undefined') {
                // 添加轨道控制器
                ice3DControls = new THREE.OrbitControls(ice3DCamera, ice3DRenderer.domElement);
                ice3DControls.enableDamping = true;
                ice3DControls.dampingFactor = 0.05;
                ice3DControls.target.set(0, 0, 0);
                console.log('轨道控制器已添加');
            } else {
                console.warn('OrbitControls不可用');
            }
            
            // 添加环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
            ice3DScene.add(ambientLight);
            
            // 添加方向光
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
            directionalLight.position.set(5, 5, 5);
            directionalLight.castShadow = true;
            ice3DScene.add(directionalLight);
            
            // 添加点光源
            const pointLight = new THREE.PointLight(0x64ffda, 0.3, 100);
            pointLight.position.set(-5, 5, 5);
            ice3DScene.add(pointLight);
            
            // 显示图例
            const legendElement = document.getElementById('ice3DLegend');
            if (legendElement) {
                legendElement.style.display = 'block';
                console.log('显示了图例');
            }
            
            console.log('3D场景初始化完成');
            hide3DIceLoading();
            
            // 启动渲染循环
            function animate() {
                ice3DAnimationId = requestAnimationFrame(animate);
                
                if (ice3DControls) {
                    ice3DControls.update();
                }
                ice3DRenderer.render(ice3DScene, ice3DCamera);
            }
            animate();
            console.log('渲染循环已启动');
            
            // 处理窗口大小变化
            window.addEventListener('resize', () => {
                const rect = container.getBoundingClientRect();
                ice3DCamera.aspect = rect.width / rect.height;
                ice3DCamera.updateProjectionMatrix();
                ice3DRenderer.setSize(rect.width, rect.height);
            });
        }
        
        // 创建特定类型的3D形状（与主页面相同的函数）
        function create3DShapeByType(coordinates, shapeType, thickness = 1.0, layers = 5) {
            const meshArray = [];
            
            if (!coordinates || coordinates.length < 3) {
                console.warn(`${shapeType} 坐标数据不足`);
                return meshArray;
            }
            
            try {
                const scale = 50;
                
                // 创建翼型形状
                const shape = new THREE.Shape();
                
                console.log(`开始创建${shapeType}翼型轮廓，共${coordinates.length}个点`);
                
                // 移动到第一个点（前缘）
                shape.moveTo(coordinates[0].x * scale, coordinates[0].y * scale);
                
                // 使用贝塞尔曲线创建平滑的翼型轮廓
                for (let i = 1; i < coordinates.length; i++) {
                    const currentPoint = coordinates[i];
                    const prevPoint = coordinates[i - 1];
                    
                    // 计算控制点以创建平滑曲线
                    const controlX = (prevPoint.x + currentPoint.x) / 2;
                    const controlY = (prevPoint.y + currentPoint.y) / 2;
                    
                    // 使用二次贝塞尔曲线连接点，创建平滑的翼型表面
                    shape.quadraticCurveTo(
                        controlX * scale, controlY * scale,
                        currentPoint.x * scale, currentPoint.y * scale
                    );
                }
                
                // 用平滑曲线闭合形状回到前缘
                const lastPoint = coordinates[coordinates.length - 1];
                const firstPoint = coordinates[0];
                const closeControlX = (lastPoint.x + firstPoint.x) / 2;
                const closeControlY = (lastPoint.y + firstPoint.y) / 2;
                
                shape.quadraticCurveTo(
                    closeControlX * scale, closeControlY * scale,
                    firstPoint.x * scale, firstPoint.y * scale
                );
                
                console.log('翼型轮廓已平滑闭合');
                
                // Z轴层叠设置
                const layerCount = layers;
                const singleLayerThickness = 0.05;
                
                // 创建多层堆叠的翼型体积
                for (let layer = 0; layer < layerCount; layer++) {
                    // 挤压设置，创建翼型体积
                    const extrudeSettings = {
                        depth: singleLayerThickness,
                        bevelEnabled: true,
                        bevelThickness: 0.01,
                        bevelSize: 0.01,
                        bevelSegments: 2,
                        steps: 1
                    };
                    
                    const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
                    
                    // 根据形状类型设置材质颜色
                    let color = 0x1a252f; // 默认深灰色
                    
                    if (shapeType === 'clean') {
                        color = 0x1f4e79;   // 深蓝色 - 干净翼型
                    } else if (shapeType === 'initial') {
                        color = 0xd68910;   // 深橙色 - 实验冰形
                    } else if (shapeType === 'predicted') {
                        color = 0xc0392b;   // 深红色 - AI预测
                    }
                    
                    // 材质设置
                    const material = new THREE.MeshPhongMaterial({
                        color: color,
                        transparent: false,
                        side: THREE.DoubleSide,
                        shininess: 50,
                        wireframe: false
                    });
                    
                    const mesh = new THREE.Mesh(geometry, material);
                    mesh.castShadow = true;
                    mesh.receiveShadow = true;
                    mesh.userData = { type: shapeType, layer: layer };
                    
                    meshArray.push(mesh);
                    
                    console.log(`第${layer + 1}层翼型: 单层厚度=${singleLayerThickness}, 颜色=#${color.toString(16)}`);
                }
                
                console.log(`${shapeType} 翼型3D创建完成：${layerCount}层`);
                
            } catch (error) {
                console.error(`创建${shapeType}翼型时出错:`, error);
            }
            
            return meshArray;
        }
        
        // 全局变量存储所有时间点的数据
        let allTimePointsData = {};
        
        // 渲染所有时间点的3D冰形预测结果
        function renderAll3DIcePredictions() {
            if (!ice3DScene) {
                console.warn('3D场景未初始化');
                return;
            }
            
            console.log('开始渲染所有时间点的3D冰形');
            
            // 清除之前的冰形网格
            ice3DMeshes.forEach(mesh => {
                ice3DScene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) mesh.material.dispose();
            });
            ice3DMeshes = [];
            
            try {
                // 创建干净机翼形状（底部，只创建一次）
                const firstTimeData = Object.values(allTimePointsData)[0];
                if (firstTimeData && firstTimeData.clean_body) {
                    const cleanBodyCoords = firstTimeData.clean_body.map(point => ({ x: point[0], y: point[1] }));
                    if (cleanBodyCoords.length >= 3) {
                        const cleanMeshes = create3DShapeByType(cleanBodyCoords, 'clean', 0.3, 1);
                        cleanMeshes.forEach((mesh, index) => {
                            mesh.position.set(0, 0, 0); // 底层位置
                            mesh.name = '干净机翼';
                            ice3DScene.add(mesh);
                            ice3DMeshes.push(mesh);
                        });
                        console.log('✅ 添加干净机翼1层（深蓝色）');
                    }
                }

                // 为每个时间点创建预测冰形
                const timePoints = [1, 2, 3, 4, 5, 6, 7, 8, 9];
                timePoints.forEach((time, timeIndex) => {
                    const data = allTimePointsData[time];
                    if (data && data.predicted_shape) {
                        const predictedShapeCoords = data.predicted_shape.map(point => ({ x: point[0], y: point[1] }));
                        
                        if (predictedShapeCoords.length >= 3) {
                            // 为不同时间点设置不同的颜色渐变
                            const colorVariations = [
                                0xe74c3c,  // T+1分钟  - 亮红色
                                0xd32f2f,  // T+2分钟 - 深红色
                                0xc62828,  // T+3分钟 - 更深红色
                                0xb71c1c,  // T+4分钟 - 暗红色
                                0x8e24aa,  // T+5分钟 - 紫红色
                                0x6a1b9a,  // T+6分钟 - 深紫色
                                0x4caf50,  // T+7分钟 - 绿色
                                0x388e3c,  // T+8分钟 - 深绿色
                                0xff9800   // T+9分钟 - 橙色
                            ];
                            
                            const predictedMeshes = create3DShapeByTypeWithColor(
                                predictedShapeCoords, 
                                'predicted', 
                                0.3, 
                                1, 
                                colorVariations[timeIndex]
                            );
                            
                            predictedMeshes.forEach((mesh, layerIndex) => {
                                // Z轴位置：基础位置 + 时间偏移（层之间没有空隙）
                                const baseZ = 0.04; // 干净机翼之上
                                const timeOffset = timeIndex * 0.04; // 减少时间点间隔，紧密堆叠
                                
                                mesh.position.set(0, 0, baseZ + timeOffset);
                                mesh.name = `AI预测冰形-T+${time}分钟`;
                                mesh.userData.timePoint = time;
                                ice3DScene.add(mesh);
                                ice3DMeshes.push(mesh);
                            });
                            
                            console.log(`✅ 添加T+${time}分钟预测冰形1层（颜色:#${colorVariations[timeIndex].toString(16)}）`);
                        }
                    }
                });
                
                // 调整相机视角以显示所有时间点
                ice3DCamera.position.set(-2.5, 2.0, -1.0);
                if (ice3DControls) {
                    ice3DControls.target.set(0, 0, 0.5); // 看向中央
                    ice3DControls.update();
                }
                
                console.log(`3D时序冰形创建完成：${ice3DMeshes.length}个网格，${Object.keys(allTimePointsData).length}个时间点`);
                
            } catch (error) {
                console.error('3D时序冰形渲染失败:', error);
            }
        }
        
        // 带颜色参数的3D形状创建函数
        function create3DShapeByTypeWithColor(coordinates, shapeType, thickness = 1.0, layers = 5, customColor = null) {
            const meshArray = [];
            
            if (!coordinates || coordinates.length < 3) {
                console.warn(`${shapeType} 坐标数据不足`);
                return meshArray;
            }
            
            try {
                const scale = 50;
                
                // 创建翼型形状
                const shape = new THREE.Shape();
                
                // 移动到第一个点（前缘）
                shape.moveTo(coordinates[0].x * scale, coordinates[0].y * scale);
                
                // 使用贝塞尔曲线创建平滑的翼型轮廓
                for (let i = 1; i < coordinates.length; i++) {
                    const currentPoint = coordinates[i];
                    const prevPoint = coordinates[i - 1];
                    
                    // 计算控制点以创建平滑曲线
                    const controlX = (prevPoint.x + currentPoint.x) / 2;
                    const controlY = (prevPoint.y + currentPoint.y) / 2;
                    
                    // 使用二次贝塞尔曲线连接点，创建平滑的翼型表面
                    shape.quadraticCurveTo(
                        controlX * scale, controlY * scale,
                        currentPoint.x * scale, currentPoint.y * scale
                    );
                }
                
                // 用平滑曲线闭合形状回到前缘
                const lastPoint = coordinates[coordinates.length - 1];
                const firstPoint = coordinates[0];
                const closeControlX = (lastPoint.x + firstPoint.x) / 2;
                const closeControlY = (lastPoint.y + firstPoint.y) / 2;
                
                shape.quadraticCurveTo(
                    closeControlX * scale, closeControlY * scale,
                    firstPoint.x * scale, firstPoint.y * scale
                );
                
                // Z轴层叠设置
                const layerCount = layers;
                const singleLayerThickness = 0.02;
                
                // 根据形状类型设置材质颜色（在循环外定义）
                let color = customColor || 0x1a252f; // 默认深灰色
                
                if (shapeType === 'clean') {
                    color = 0x1f4e79;   // 深蓝色 - 干净翼型
                } else if (shapeType === 'predicted' && !customColor) {
                    color = 0xc0392b;   // 深红色 - AI预测
                }
                
                // 创建多层堆叠的翼型体积
                for (let layer = 0; layer < layerCount; layer++) {
                    // 挤压设置，创建翼型体积
                    const extrudeSettings = {
                        depth: singleLayerThickness,
                        bevelEnabled: true,
                        bevelThickness: 0.005,
                        bevelSize: 0.005,
                        bevelSegments: 1,
                        steps: 1
                    };
                    
                    const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
                    
                    // 材质设置 - 去掉透明度，所有形状都不透明
                    const material = new THREE.MeshPhongMaterial({
                        color: color,
                        transparent: false,
                        opacity: 1.0,
                        side: THREE.DoubleSide,
                        shininess: 60,
                        wireframe: false
                    });
                    
                    const mesh = new THREE.Mesh(geometry, material);
                    mesh.castShadow = true;
                    mesh.receiveShadow = true;
                    mesh.userData = { type: shapeType, layer: layer };
                    
                    meshArray.push(mesh);
                }
                
                console.log(`${shapeType} 翼型3D创建完成：${layerCount}层，颜色:#${color.toString(16)}`);
                
            } catch (error) {
                console.error(`创建${shapeType}翼型时出错:`, error);
            }
            
            return meshArray;
        }
        
        const conditions = JSON.parse('{{ conditions | tojson | safe }}');

        // 动态更新工况信息
        document.getElementById('velocity').textContent = conditions.Velocity;
        document.getElementById('temperature').textContent = conditions.Temperature;
        document.getElementById('lwc').textContent = conditions.LWC;
        document.getElementById('mvd').textContent = conditions.MVD;
        document.getElementById('height').textContent = conditions.Height || 0;
        document.getElementById('aoa').textContent = conditions.AOA || 0;

        const timePoints = [1, 2, 3, 4, 5, 6, 7, 8, 9];
        const chartIds = ["chart1min", "chart2min", "chart3min", "chart4min", "chart5min", "chart6min", "chart7min", "chart8min", "chart9min"];

        timePoints.forEach((time, index) => {
            // 动态逐点绘制预测冰形（增强版动画）
            let currentIndex = 0;
            const interval = 10; // 每点绘制的时间间隔（毫秒）- 更快的动画速度
            let animationPhase = 'drawing'; // 'drawing' 或 'complete'

            const drawStep = (chart, predictedShape) => {
                if (animationPhase === 'drawing' && currentIndex < predictedShape.length) {
                    const nextPoint = predictedShape[currentIndex];
                    chart.data.datasets[1].data.push(nextPoint); // 添加单个点
                    chart.update('none'); // 无动画更新以保持流畅
                    currentIndex++;
                    
                    // 进度提示
                    const progress = Math.round((currentIndex / predictedShape.length) * 100);
                    if (progress % 25 === 0) { // 每25%显示一次进度
                        //console.log(`T+${time}分钟 AI预测进度: ${progress}%`);
                    }
                    
                } else if (animationPhase === 'drawing') {
                    // 绘制完成，进入完成阶段
                    animationPhase = 'complete';
                    //console.log(`🎉 T+${time}分钟 AI预测冰形绘制完成！`);
                    
                    // 添加完成动画效果
                    chart.data.datasets[1].borderWidth = 8;  // 完成时更粗的线条
                    chart.data.datasets[1].pointRadius = 3;
                    chart.update({
                        duration: 600,
                        easing: 'easeInOutElastic'
                    });
                    
                    // 4秒后重置动画
                    setTimeout(() => {
                        animationPhase = 'drawing';
                        currentIndex = 0;
                        chart.data.datasets[1].data = []; // 清空数据
                        chart.data.datasets[1].borderWidth = 6;  // 重置时保持加粗线条
                        chart.data.datasets[1].pointRadius = 2;
                        chart.update({
                            duration: 400,
                            easing: 'easeInOutQuart'
                        });
                        //console.log(`🔄 T+${time}分钟 重新开始AI预测动画...`);
                    }, 4000); // 停留4秒
                }
            };

            const startAnimation = (chart, predictedShape) => {
                currentIndex = 0;
                animationPhase = 'drawing';
                chart.data.datasets[1].data = []; // 恢复为第2个数据集（索引1）
                chart.update();
                setInterval(() => drawStep(chart, predictedShape), interval);
            };

            fetch(`/get_icing_shape?time=${time}&velocity=${conditions.Velocity}&temperature=${conditions.Temperature}&lwc=${conditions.LWC}&mvd=${conditions.MVD}&height=${conditions.Height || 0}&aoa=${conditions.AOA || 0}`)
                .then(response => response.json())
                .then(data => {
                    // 存储数据以供3D渲染使用
                    allTimePointsData[time] = data;
                    
                    // 检查是否所有时间点数据都已获取
                    if (Object.keys(allTimePointsData).length === timePoints.length && ice3DScene) {
                        console.log('所有时间点数据已收集完成，开始渲染3D时序冰形');
                        renderAll3DIcePredictions();
                    }
                    
                    const ctx = document.getElementById(chartIds[index]).getContext('2d');

                            // 过滤前缘部分数据
                            const filterLeadingEdge = (shape, threshold = 0.05) => {
                                return shape.filter(point => point[0] <= threshold);
                            };





                            const cleanBody = filterLeadingEdge(data.clean_body).map(point => ({ x: point[0], y: point[1] }));
                            // 使用完整的预测数据，不进行过滤，通过图表配置来限制显示区域
                            const predictedShape = data.predicted_shape.map(point => ({ x: point[0], y: point[1] }));

                            const chart = new Chart(ctx, {
                                type: 'scatter',
                                data: {
                                    datasets: [
                                        {
                                            label: '🛩️ 干净机翼',
                                            data: cleanBody,
                                            borderColor: '#2c3e50',
                                            backgroundColor: 'rgba(44, 62, 80, 0.1)',
                                            borderWidth: 5,  // 加粗干净机翼线条
                                            pointRadius: 0,
                                            showLine: true,
                                            fill: false,
                                            tension: 0.1
                                        },
                                        {
                                            label: `🧊 T+${time}分钟 AI预测冰形`,
                                            data: [], // 初始为空
                                            borderColor: '#e74c3c',
                                            backgroundColor: 'rgba(231, 76, 60, 0.3)',
                                            borderWidth: 6,  // 加粗AI预测冰形线条
                                            pointRadius: 2,
                                            pointBackgroundColor: '#e74c3c',
                                            pointBorderColor: '#ffffff',
                                            pointBorderWidth: 2,
                                            showLine: true,
                                            fill: false,
                                            tension: 0.2,
                                            pointHoverRadius: 6,
                                            pointHoverBackgroundColor: '#c0392b',
                                            pointHoverBorderColor: '#ffffff',
                                            pointHoverBorderWidth: 3
                                        }
                                    ]
                                },
                                options: {
                                    responsive: true,
                                    animation: {
                                        duration: 1000,
                                        easing: 'easeInOutQuart'
                                    },
                                    plugins: {
                                        legend: {
                                            display: true,
                                            position: 'top',
                                            labels: {
                                                usePointStyle: true,
                                                pointStyle: 'line',
                                                font: {
                                                    size: 12,
                                                    weight: 'bold'
                                                },
                                                color: '#2c3e50',
                                                padding: 15
                                            }
                                        },
                                        tooltip: {
                                            enabled: true,
                                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                            titleColor: '#ffffff',
                                            bodyColor: '#ffffff',
                                            borderColor: '#667eea',
                                            borderWidth: 2,
                                            cornerRadius: 8,
                                            displayColors: true,
                                            callbacks: {
                                                title: function(context) {
                                                    return '坐标信息';
                                                },
                                                label: function(context) {
                                                    return `${context.dataset.label}: (${context.parsed.x.toFixed(4)}, ${context.parsed.y.toFixed(4)})`;
                                                }
                                            }
                                        },
                                        zoom: {
                                            pan: {
                                                enabled: true,
                                                mode: 'xy'
                                            },
                                            zoom: {
                                                wheel: {
                                                    enabled: true
                                                },
                                                pinch: {
                                                    enabled: true
                                                },
                                                mode: 'xy'
                                            }
                                        }
                                    },
                                    scales: {
                                        x: {
                                            title: {
                                                display: true,
                                                text: '弦向坐标 X (m)',
                                                font: {
                                                    size: 13,
                                                    weight: 'bold'
                                                },
                                                color: '#2c3e50'
                                            },
                                            grid: {
                                                display: false
                                            },
                                            ticks: {
                                                font: {
                                                    size: 11
                                                },
                                                color: '#2c3e50'
                                            },
                                            max: 0.03  // 临时扩大显示范围，观察是否有变化
                                        },
                                        y: {
                                            title: {
                                                display: true,
                                                text: '法向坐标 Y (m)',
                                                font: {
                                                    size: 13,
                                                    weight: 'bold'
                                                },
                                                color: '#2c3e50'
                                            },
                                            grid: {
                                                display: false
                                            },
                                            ticks: {
                                                font: {
                                                    size: 11
                                                },
                                                color: '#2c3e50'
                                            }
                                        }
                                    },
                                    interaction: {
                                        intersect: false,
                                        mode: 'nearest'
                                    }
                                }
                            });

                            startAnimation(chart, predictedShape);
                        })
                        .catch(error => console.error(`Error fetching data for ${time} seconds:`, error));
        });
        
        // 页面加载后初始化3D可视化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，准备初始化3D可视化...');
            
            // 等待一小段时间确保所有资源加载完成
            setTimeout(() => {
                init3DIceVisualization();
                
                // 如果有历史数据，也显示在3D视图中 - 使用第一个时间点的数据
                if (typeof results !== 'undefined' && results && results.length > 0) {
                    console.log('使用历史数据渲染3D冰形:', results[0]);
                    render3DIcePrediction(results[0]);
                }
            }, 100);
            
            // 为所有时间点容器添加点击事件监听器
            const chartContainers = document.querySelectorAll('.chart-container[data-time]');
            chartContainers.forEach(container => {
                container.addEventListener('click', function() {
                    const timePoint = this.getAttribute('data-time');
                    console.log(`用户点击了T+${timePoint}分钟时间点`);
                    
                    // 高亮对应的3D冰形层
                    if (ice3DScene) {
                        // 重置所有网格的发光效果，保持不透明
                        ice3DMeshes.forEach(mesh => {
                            if (mesh.userData.timePoint) {
                                mesh.material.opacity = 1.0; // 保持不透明
                                mesh.material.emissive.setHex(0x000000);
                            }
                        });
                        
                        // 高亮选中时间点的网格
                        ice3DMeshes.forEach(mesh => {
                            if (mesh.userData.timePoint == timePoint) {
                                mesh.material.opacity = 1.0;
                                mesh.material.emissive.setHex(0x444444); // 发光效果
                                console.log(`高亮了T+${timePoint}分钟的3D冰形`);
                            }
                        });
                        
                        // 更新3D容器标题
                        const title = document.querySelector('.ice3d-container h3');
                        if (title) {
                            title.innerHTML = `🔬 3D时序冰形可视化 - 当前聚焦: T+${timePoint}分钟`;
                        }
                    }
                    
                    // 高亮选中的时间点容器
                    chartContainers.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });
            
            // 默认选中第一个时间点
            if (chartContainers.length > 0) {
                chartContainers[0].classList.add('selected');
            }
            
            // 添加"显示全部时间点"按钮事件
            const showAllTimesBtn = document.getElementById('showAllTimesBtn');
            if (showAllTimesBtn) {
                showAllTimesBtn.addEventListener('click', function() {
                    console.log('用户点击了显示全部时间点按钮');
                    
                    // 重置所有网格为正常显示，保持不透明
                    if (ice3DScene) {
                        ice3DMeshes.forEach(mesh => {
                            if (mesh.userData.timePoint) {
                                mesh.material.opacity = 1.0; // 保持不透明
                                mesh.material.emissive.setHex(0x000000); // 移除发光
                            } else if (mesh.name === '干净机翼') {
                                mesh.material.opacity = 1.0;
                            }
                        });
                        
                        // 更新标题
                        const title = document.querySelector('.ice3d-container h3');
                        if (title) {
                            title.innerHTML = '🔬 3D时序冰形可视化 - 6个时间点演化';
                        }
                        
                        // 清除选中状态
                        chartContainers.forEach(c => c.classList.remove('selected'));
                        
                        console.log('已重置为显示所有时间点');
                    }
                });
            }
            
            console.log(`已为${chartContainers.length}个时间点容器添加点击事件`);
        });
    </script>
</body>
</html>
