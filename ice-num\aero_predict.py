import os
import torch
import numpy as np
import pickle
from model1 import PointTransformerWithFiLM
from scipy.interpolate import splprep, splev

# ✅ 设定设备
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# ✅ 环境参数归一化
ENV_MIN = np.array([0.1, 15, -35, 0])
ENV_MAX = np.array([1.5, 60, -10, 15])

# ✅ 获取当前脚本所在目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# ✅ 加载气动系数反归一化参数
aero_norm_path = os.path.join(SCRIPT_DIR, "aero_norm.pkl")
with open(aero_norm_path, "rb") as f:
    aero_stats = pickle.load(f)
    AERO_MEAN = aero_stats["mean"]
    AERO_STD = aero_stats["std"]

def denormalize(data, mean, std):
    return data * std + mean

def compute_curvature_spline(x, y, smoothing=0.001):
    tck, u = splprep([x, y], s=smoothing)
    dx, dy = splev(u, tck, der=1)
    ddx, ddy = splev(u, tck, der=2)
    curvature = np.abs(dx * ddy - dy * ddx) / (dx**2 + dy**2)**1.5
    curvature[np.isnan(curvature)] = 0
    curvature[np.isinf(curvature)] = 0
    return curvature

def normalize_k(k_values, method="minmax"):
    if method == "minmax":
        return (k_values - np.min(k_values)) / (np.max(k_values) - np.min(k_values) + 1e-8)
    elif method == "zscore":
        return (k_values - np.mean(k_values)) / (np.std(k_values) + 1e-8)
    else:
        raise ValueError("Unsupported normalization method!")

def load_point_cloud_from_file(file_path):
    """从文件加载点云数据，支持2D和3D"""
    data = np.loadtxt(file_path)
    if data.shape[1] == 2:
        print(f"检测到二维翼型，自动转换为三维：{file_path}")
        x, y = data[:, 0], data[:, 1]
        k = compute_curvature_spline(x, y, smoothing=0.001)
        k_norm = normalize_k(k, method="minmax")
        return np.column_stack((x, y, k_norm))
    elif data.shape[1] == 3:
        print(f"检测到三维点云：{file_path}")
        return data
    else:
        raise ValueError(f"不支持的数据维度：{file_path}")

def get_latest_model():
    """获取最新的模型文件"""
    model_dir = os.path.join(SCRIPT_DIR, "Model-68-Dropout3-EnhancedFilm")
    if not os.path.exists(model_dir):
        return None
    model_files = [f for f in os.listdir(model_dir) if f.endswith(".pth")]
    if not model_files:
        return None
    latest = max(model_files, key=lambda x: int(x.split("_")[1].split(".")[0]))
    return os.path.join(model_dir, latest)

def load_model():
    """加载预训练模型"""
    model_path = get_latest_model()
    if not model_path:
        raise FileNotFoundError("未找到训练模型")
    
    print(f"✅ 加载模型: {model_path}")
    model = PointTransformerWithFiLM(input_dim=3, hidden_dim=64, num_heads=4, dropout=0.3).to(DEVICE)
    model.load_state_dict(torch.load(model_path, map_location=DEVICE))
    model.eval()
    return model

def predict_aerodynamic_coefficients(iceshape_file_path, lwc=0.5, mvd=20, temperature=-15):
    """
    预测气动系数
    
    Args:
        iceshape_file_path (str): 冰形文件路径 (data/iceshape-predict.txt)
        lwc (float): 液态水含量 (g/m³)
        mvd (float): 水滴直径 (μm)  
        temperature (float): 来流温度 (°C)
    
    Returns:
        dict: 包含升力系数和阻力系数的预测结果
    """
    try:
        # 加载模型
        model = load_model()
        
        # 加载冰形数据
        points = load_point_cloud_from_file(iceshape_file_path)
        print(f"✅ 成功加载冰形数据: {iceshape_file_path}, 点数: {len(points)}")
        
        # 预测不同攻角下的气动系数
        aoas = [0, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
        predictions = []
        
        with torch.no_grad():
            for aoa in aoas:
                # 构建环境参数
                env = np.array([lwc, mvd, temperature, aoa])
                env_norm = (env - ENV_MIN) / (ENV_MAX - ENV_MIN)
                
                # 转换为张量
                env_tensor = torch.tensor(env_norm, dtype=torch.float32).unsqueeze(0).to(DEVICE)
                pts_tensor = torch.tensor(points, dtype=torch.float32).unsqueeze(0).to(DEVICE)
                
                # 预测
                pred = model(pts_tensor, env_tensor).cpu().numpy()[0]
                # 反归一化
                denorm_pred = denormalize(pred, AERO_MEAN, AERO_STD)
                predictions.append(denorm_pred)
        
        predictions = np.array(predictions)
        
        # 组织返回结果
        result = {
            "success": True,
            "conditions": {
                "LWC": lwc,
                "MVD": mvd,
                "Temperature": temperature
            },
            "aoa": aoas,
            "lift_coefficient": predictions[:, 0].tolist(),    # 升力系数
            "drag_coefficient": predictions[:, 1].tolist(),    # 阻力系数  
            "moment_coefficient": predictions[:, 2].tolist()   # 力矩系数
        }
        
        print(f"✅ 预测完成，攻角范围: {min(aoas)}° - {max(aoas)}°")
        return result
        
    except Exception as e:
        print(f"❌ 预测失败: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# 测试函数
def test_prediction():
    """测试预测功能"""
    # 使用绝对路径或根据脚本运行位置调整路径
    # 如果从flask-all-ui根目录运行，使用 "data/iceshape-predict.txt"
    # 如果从ice-num目录运行，使用 "../data/iceshape-predict.txt"
    
    # 智能路径检测
    possible_paths = [
        "../data/iceshape-predict.txt",  # 从ice-num目录运行
        "data/iceshape-predict.txt",     # 从根目录运行
        os.path.join(os.path.dirname(SCRIPT_DIR), "data", "iceshape-predict.txt")  # 绝对路径
    ]
    
    iceshape_path = None
    for path in possible_paths:
        if os.path.exists(path):
            iceshape_path = path
            break
    
    if not iceshape_path:
        print("❌ 找不到冰形文件 iceshape-predict.txt")
        print("检查的路径:")
        for path in possible_paths:
            print(f"  - {path} (存在: {os.path.exists(path)})")
        return
    
    # 默认工况
    lwc = 0.5      # 液态水含量 (g/m³)
    mvd = 20       # 水滴直径 (μm)
    temp = -15     # 温度 (°C)
    
    print("=== 气动系数预测测试 ===")
    print(f"冰形文件: {iceshape_path}")
    print(f"工况参数: LWC={lwc}, MVD={mvd}, Temperature={temp}°C")
    print("-" * 50)
    
    result = predict_aerodynamic_coefficients(iceshape_path, lwc, mvd, temp)
    
    if result["success"]:
        print("✅ 预测成功!")
        print(f"攻角: {result['aoa']}")
        print(f"升力系数: {[f'{x:.4f}' for x in result['lift_coefficient']]}...")  # 显示前5个
        print(f"阻力系数: {[f'{x:.4f}' for x in result['drag_coefficient']]}...")  # 显示前5个
    else:
        print(f"❌ 预测失败: {result['error']}")

if __name__ == "__main__":
    test_prediction()
