import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'  # 添加这行
import torch
import numpy as np
import json
import matplotlib.pyplot as plt
import traceback  # <-- 修正点1：补上缺失的导入


# --- 从模型文件中导入我们定义的网络 ---
from model import PhysEvolveNet


# ==============================================================================
# 1. 辅助函数
# ==============================================================================
def load_airfoil_2d(filepath):
    try:
        data = np.loadtxt(filepath)
        return data if data.ndim == 2 and data.shape[1] == 2 else None
    except:
        return None


def load_airfoil_3d(filepath):
    try:
        data = np.loadtxt(filepath)
        return data if data.ndim == 2 and data.shape[1] == 3 else None
    except:
        return None


def normalize_conditions(cond_data, stats):
    min_val = np.array(stats['conditions']['min'])
    max_val = np.array(stats['conditions']['max'])
    denominator = max_val - min_val
    denominator[denominator == 0] = 1e-9
    return (cond_data - min_val) / denominator


# ==============================================================================
# 2. 核心预测与可视化函数
# ==============================================================================
def predict_and_visualize(config):
    """
    加载模型，对单个样本进行预测，并生成专业的多重颜色、加粗放大版对比图。
    """
    # --- 1. 设置与模型加载 ---
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"正在使用设备: {device}")
    print(f"正在从 {config['MODEL_PATH']} 加载已训练好的模型...")
    model = PhysEvolveNet()

    # 检查文件路径是否正确
    if not os.path.exists(config['MODEL_PATH']):
        print(f"错误: 模型文件路径不存在: {config['MODEL_PATH']}")
        return

    # 检查文件是否可读
    try:
        with open(config['MODEL_PATH'], 'rb') as f:
            pass
    except Exception as e:
        print(f"错误: 无法读取模型文件: {e}")
        return

    # 测试加载模型
    try:
        model.load_state_dict(torch.load(config['MODEL_PATH'], map_location=device))
    except Exception as e:
        print(f"错误: 加载模型失败: {e}")
        return

    model.to(device)
    model.eval()
    print("模型加载成功。")

    # --- 2. 加载并准备数据 ---
    print("正在准备输入数据...")
    with open(config['STATS_FILE_PATH'], 'r') as f:
        stats = json.load(f)
    clean_body_2d = load_airfoil_2d(config['CLEAN_BODY_PATH'])
    if clean_body_2d is None: return
    if len(clean_body_2d) != config['NUM_POINTS']: raise ValueError("干净翼型点数与模型要求不匹配")
    clean_body_tensor = torch.tensor(clean_body_2d, dtype=torch.float32)
    initial_shape_3d = load_airfoil_3d(config['INITIAL_SHAPE_PATH'])
    if initial_shape_3d is None: return
    if len(initial_shape_3d) != config['NUM_POINTS']: raise ValueError("初始冰形点数与模型要求不匹配")
    ground_truth_shape_3d = load_airfoil_3d(config['GROUND_TRUTH_SHAPE_PATH'])
    if ground_truth_shape_3d is None: return

    # --- 3. 构建模型输入 ---
    input_geo_5d = torch.cat([torch.tensor(initial_shape_3d, dtype=torch.float32), clean_body_tensor], dim=1)
    cond_vector_orig = np.array([
        config['CONDITIONS']['Velocity'], config['CONDITIONS']['Temperature'],
        config['CONDITIONS']['LWC'], config['CONDITIONS']['MVD'], config['DELTA_T']
    ])
    cond_vector_norm = normalize_conditions(cond_vector_orig, stats)
    input_geo_seq = input_geo_5d.unsqueeze(0).unsqueeze(0).float().to(device)
    input_cond_seq = torch.tensor(cond_vector_norm).unsqueeze(0).unsqueeze(0).float().to(device)
    h_initial = torch.zeros(1, 256).float().to(device)
    print("输入数据准备完毕。")

    # --- 4. 执行预测 ---
    print("正在执行模型预测...")
    with torch.no_grad():
        predicted_displacement_seq = model(input_geo_seq, input_cond_seq, h_initial)
    predicted_displacement = predicted_displacement_seq.squeeze().cpu().numpy()
    print("预测完成。")

    # --- 5. 计算最终形状 ---
    initial_shape_coords = initial_shape_3d[:, 0:2]
    predicted_shape_coords = initial_shape_coords + predicted_displacement
    ground_truth_shape_coords = ground_truth_shape_3d[:, 0:2]

    # --- 6. 可视化对比 ---
    print("正在生成最终对比图...")
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.rcParams['font.family'] = 'Times New Roman'
    plt.rcParams['font.size'] = 16  # 放大基础字号

    fig, ax = plt.subplots(figsize=(12, 10))

    # --- 核心修改：分层绘制以达到您的要求 ---
    # 1. 先用黑色绘制完整的干净翼型轮廓作为基准
    front_edge_mask = (clean_body_2d[:, 0] >= config['PLOT_XLIM'][0]) & (clean_body_2d[:, 0] <= config['PLOT_XLIM'][1])
    ax.plot(clean_body_2d[front_edge_mask, 0], clean_body_2d[front_edge_mask, 1],
            color='black', linestyle='-', linewidth=2.5,
            label='Clean Airfoil')

    # 2. 在上面用蓝色绘制真实的结冰轮廓
    ground_truth_mask = (ground_truth_shape_coords[:, 0] >= config['PLOT_XLIM'][0]) & (ground_truth_shape_coords[:, 0] <= config['PLOT_XLIM'][1])
    ax.plot(ground_truth_shape_coords[ground_truth_mask, 0], ground_truth_shape_coords[ground_truth_mask, 1],
            color='blue', linestyle='-', marker='o', markersize=3.5, linewidth=2.5,
            label='Ground Truth')

    # 3. 最后用橙色虚线绘制预测的轮廓
    predicted_mask = (predicted_shape_coords[:, 0] >= config['PLOT_XLIM'][0]) & (predicted_shape_coords[:, 0] <= config['PLOT_XLIM'][1])
    ax.plot(predicted_shape_coords[predicted_mask, 0], predicted_shape_coords[predicted_mask, 1],
            color='orange', linestyle='--', marker='x', markersize=4, linewidth=2.5,
            label='Prediction')

    # 使用您手动设定的范围
    if config.get('PLOT_XLIM') and config.get('PLOT_YLIM'):
        xlim, ylim = config['PLOT_XLIM'], config['PLOT_YLIM']
        x_center, y_center = sum(xlim) / 2, sum(ylim) / 2
        x_range, y_range = xlim[1] - xlim[0], ylim[1] - ylim[0]
        max_range = max(x_range, y_range)
        final_xlim = [x_center - max_range / 2, x_center + max_range / 2]
        final_ylim = [y_center - max_range / 2, y_center + max_range / 2]
        ax.set_xlim(final_xlim)
        ax.set_ylim(final_ylim)

    ax.set_title('Ground Truth vs. Prediction on Leading Edge', fontsize=20, weight='bold')
    ax.set_xlabel('X-coordinate (m)', fontsize=16)
    ax.set_ylabel('Y-coordinate (m)', fontsize=16)
    ax.legend(fontsize=14, frameon=True)
    ax.grid(True, linestyle='--', alpha=0.7)
    ax.set_aspect('equal', adjustable='box')

    plt.show()

# ==============================================================================
# 主执行块
# ==============================================================================
# ==============================================================================
# 主执行块
# ==============================================================================
if __name__ == '__main__':
    # --- 1. 请在这里配置您的测试参数 ---

    config = {
        'MODEL_PATH': 'best_model.pth',
        'STATS_FILE_PATH': 'stats.json',

        'INITIAL_SHAPE_PATH': '../data/iceshape-predict.txt',
        'GROUND_TRUTH_SHAPE_PATH': '../data/iceshape-true.txt',
        'CLEAN_BODY_PATH': 'body.txt',

        'NUM_POINTS': 262,

        'CONDITIONS': {
            'Velocity': 128.0,
            'Temperature': 253.0,
            'LWC': 0.43,
            'MVD': 35.0
        },

        'DELTA_T': 60.0,

        # --- 核心修改：在这里手动定义您想要的绘图范围 ---
        'PLOT_XLIM': None,  # 移除 X 轴范围限制
        'PLOT_YLIM': None  # 移除 Y 轴范围限制
    }

    # --- 2. 运行预测和可视化 ---
    try:
        predict_and_visualize(config)
    except FileNotFoundError as e:
        print(f"\n错误：找不到必要的文件。请检查配置中的路径是否正确。")
        print(f"详细信息: {e}")
    except Exception as e:
        print(f"\n程序运行中发生未知错误: {e}")
        traceback.print_exc()