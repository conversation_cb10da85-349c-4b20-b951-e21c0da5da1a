import torch
import torch.nn as nn
import torch.nn.functional as F

class LocalTransformer(nn.Module):
    """
    局部 Transformer 模块：处理局部点云特征，提高局部细节学习能力。
    """
    def __init__(self, dim, num_heads=4):
        super().__init__()
        self.attn = nn.MultiheadAttention(embed_dim=dim, num_heads=num_heads, batch_first=True)
        self.ffn = nn.Sequential(
            nn.Linear(dim, dim),
            nn.ReLU(),
            nn.Linear(dim, dim)
        )
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)

    def forward(self, x):
        attn_out, _ = self.attn(x, x, x)
        x = self.norm1(x + attn_out)
        x = self.norm2(x + self.ffn(x))
        return x

class GlobalTransformer(nn.Module):
    """
    全局 Transformer 模块：汇总全局点云信息，确保全局一致性。
    """
    def __init__(self, dim, num_heads=4):
        super().__init__()
        self.attn = nn.MultiheadAttention(embed_dim=dim, num_heads=num_heads, batch_first=True)
        self.ffn = nn.Sequential(
            nn.Linear(dim, dim),
            nn.ReLU(),
            nn.Linear(dim, dim)
        )
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)

    def forward(self, x):
        attn_out, _ = self.attn(x, x, x)
        x = self.norm1(x + attn_out)
        x = self.norm2(x + self.ffn(x))
        return x

class PointTransformer(nn.Module):
    """
    PointTransformer 模型：
    - 局部 Transformer 先处理局部特征
    - 全局 Transformer 处理整体结构
    """
    def __init__(self, input_dim=3, hidden_dim=64, num_heads=4):
        super().__init__()
        self.input_mlp = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        self.local_transformer = LocalTransformer(hidden_dim, num_heads)
        self.global_transformer = GlobalTransformer(hidden_dim, num_heads)

    def forward(self, points):
        """
        :param points: (batch_size, num_points, input_dim) 输入点云数据 (x, y, z)
        :return: (batch_size, num_points, hidden_dim) 经过 Transformer 处理后的点云特征
        """
        x = self.input_mlp(points)  # 先用 MLP 进行特征提取
        x = self.local_transformer(x)  # 局部 Transformer
        x = self.global_transformer(x)  # 全局 Transformer
        return x
if __name__ == "__main__":
    # 假设我们有 batch_size = 4, num_points = 519, input_dim = 3
    batch_size = 4
    num_points = 519
    input_dim = 3  # (x, y, z)

    # 生成随机点云数据
    test_points = torch.randn(batch_size, num_points, input_dim)

    # 初始化模型
    model = PointTransformer(input_dim=3, hidden_dim=64, num_heads=4)

    # 运行模型
    output = model(test_points)

    # 打印输出形状
    print("输入点云形状:", test_points.shape)  # (4, 519, 3)
    print("输出特征形状:", output.shape)  # (4, 519, 64)
