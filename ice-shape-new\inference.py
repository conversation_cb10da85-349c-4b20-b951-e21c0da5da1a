#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新版PhysEvolveNet推理脚本
输入：初始冰形 + 工况 + 时间 -> 输出：预测冰形
"""

import os
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import argparse
import json
import pickle

from model import create_model
from dataset import IceShapeDataset


class IceShapePredictor:
    """冰形预测器"""

    def __init__(self, model_path, config_path=None, scaler_path=None, device=None):
        """
        Args:
            model_path: 模型权重文件路径
            config_path: 配置文件路径
            scaler_path: 归一化器文件路径
            device: 计算设备
        """
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.scaler_path = scaler_path
        
        # 加载配置
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r') as f:
                self.config = json.load(f)
        else:
            # 默认配置
            self.config = {
                'd_model': 256,
                'nhead': 8,
                'num_encoder_layers': 6,
                'num_decoder_layers': 3,
                'dropout': 0.1
            }
        
        # 创建模型
        self.model = create_model(
            input_dim=10,
            d_model=self.config['d_model'],
            nhead=self.config['nhead'],
            num_encoder_layers=self.config['num_encoder_layers'],
            num_decoder_layers=self.config['num_decoder_layers'],
            output_dim=2,  # 改为2维输出
            dropout=self.config['dropout']
        ).to(self.device)
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.model.load_state_dict(checkpoint)
        
        self.model.eval()

        # 加载归一化器
        self.dataset = None
        if self.scaler_path and os.path.exists(self.scaler_path):
            # 直接加载归一化器，不创建数据集
            with open(self.scaler_path, 'rb') as f:
                scalers = pickle.load(f)
                self.coord_scaler = scalers['coord_scaler']
                self.condition_scaler = scalers['condition_scaler']
                self.time_scaler = scalers['time_scaler']
                self.normalize = True
            print(f"归一化器加载完成")
        else:
            self.coord_scaler = None
            self.condition_scaler = None
            self.time_scaler = None
            self.normalize = False

        print(f"模型加载完成，设备: {self.device}")

    def predict_ice_shape(self, initial_shape, conditions, time_delta):
        """
        预测冰形

        Args:
            initial_shape: [N, 3] 初始冰形坐标 (x, y, z) - z用于增强特征
            conditions: [6] 工况参数 [H, V, T, LWC, MVD, AOA]
            time_delta: float 时间差

        Returns:
            predicted_shape: [N, 2] 预测的冰形坐标 (x, y)
        """
        with torch.no_grad():
            # 准备输入数据
            num_points = initial_shape.shape[0]

            # 应用归一化
            if self.normalize:
                initial_shape_norm = self.coord_scaler.transform(initial_shape)
                conditions_norm = self.condition_scaler.transform(conditions.reshape(1, -1)).flatten()
                time_delta_norm = self.time_scaler.transform(np.array([[time_delta]])).flatten()[0]
            else:
                initial_shape_norm = initial_shape
                conditions_norm = conditions
                time_delta_norm = time_delta

            # 扩展工况和时间到每个点
            conditions_expanded = np.tile(conditions_norm, (num_points, 1))  # [N, 6]
            time_delta_expanded = np.full((num_points, 1), time_delta_norm)   # [N, 1]

            # 拼接输入特征 [N, 10]
            input_features = np.hstack([initial_shape_norm, conditions_expanded, time_delta_expanded])

            # 转换为tensor并添加batch维度
            input_tensor = torch.FloatTensor(input_features).unsqueeze(0).to(self.device)  # [1, N, 10]

            # 预测
            predicted_shape_norm = self.model(input_tensor)  # [1, N, 2]

            # 转换回numpy
            predicted_shape_norm = predicted_shape_norm.squeeze(0).cpu().numpy()  # [N, 2]

            # 反归一化 - 只对x,y坐标反归一化
            if self.normalize:
                # 创建临时的3维数组用于反归一化（补充z=0）
                temp_3d = np.zeros((predicted_shape_norm.shape[0], 3))
                temp_3d[:, :2] = predicted_shape_norm
                temp_3d_denorm = self.coord_scaler.inverse_transform(temp_3d)
                predicted_shape = temp_3d_denorm[:, :2]  # 只取x,y
            else:
                predicted_shape = predicted_shape_norm

            return predicted_shape
    
    def predict_sequence(self, initial_shape, conditions, time_steps):
        """
        预测时间序列
        
        Args:
            initial_shape: [N, 3] 初始冰形
            conditions: [6] 工况参数
            time_steps: list 时间步列表
            
        Returns:
            predicted_shapes: list of [N, 3] 预测的冰形序列
        """
        predicted_shapes = [initial_shape.copy()]
        current_shape = initial_shape.copy()
        
        for i, target_time in enumerate(time_steps[1:], 1):
            time_delta = target_time - time_steps[i-1]
            
            # 预测下一个时间步的冰形
            next_shape = self.predict_ice_shape(current_shape, conditions, time_delta)
            predicted_shapes.append(next_shape)
            
            # 更新当前冰形
            current_shape = next_shape
        
        return predicted_shapes


def visualize_ice_shapes(shapes_list, labels=None, save_path=None):
    """可视化冰形"""
    fig = plt.figure(figsize=(15, 5))
    
    # 2D视图 (x-y平面)
    ax1 = fig.add_subplot(131)
    for i, shape in enumerate(shapes_list):
        label = labels[i] if labels else f'Shape {i}'
        ax1.plot(shape[:, 0], shape[:, 1], 'o-', markersize=2, label=label)
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_title('Ice Shape (X-Y View)')
    ax1.legend()
    ax1.grid(True)
    ax1.axis('equal')
    
    # 形状变化对比
    ax2 = fig.add_subplot(132)
    for i, shape in enumerate(shapes_list):
        label = labels[i] if labels else f'Shape {i}'
        # 计算每个点到原点的距离作为形状特征
        distances = np.sqrt(shape[:, 0]**2 + shape[:, 1]**2)
        ax2.plot(distances, 'o-', markersize=2, label=label)
    ax2.set_xlabel('Point Index')
    ax2.set_ylabel('Distance from Origin')
    ax2.set_title('Shape Evolution (Distance)')
    ax2.legend()
    ax2.grid(True)

    # 形状轮廓对比（放大视图）
    ax3 = fig.add_subplot(133)
    for i, shape in enumerate(shapes_list):
        label = labels[i] if labels else f'Shape {i}'
        ax3.plot(shape[:, 0], shape[:, 1], 'o-', markersize=1, label=label, alpha=0.7)
    ax3.set_xlabel('X')
    ax3.set_ylabel('Y')
    ax3.set_title('Ice Shape (Detailed View)')
    ax3.legend()
    ax3.grid(True)
    ax3.axis('equal')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"可视化结果保存到: {save_path}")
    
    plt.show()


def main():
    parser = argparse.ArgumentParser(description='PhysEvolveNet Inference')
    parser.add_argument('--model_path', type=str, required=True, help='Model checkpoint path')
    parser.add_argument('--config_path', type=str, help='Config file path')
    parser.add_argument('--scaler_path', type=str, help='Scaler file path')
    parser.add_argument('--data_root', type=str, default='result-all', help='Data root directory')
    parser.add_argument('--cases_file', type=str, default='Cases.txt', help='Cases file path')
    parser.add_argument('--case_idx', type=int, default=0, help='Case index to test')
    parser.add_argument('--start_time', type=int, default=0, help='Start time step')
    parser.add_argument('--target_times', type=str, default='1,2,3,4,5', help='Target time steps (comma separated)')
    parser.add_argument('--output_dir', type=str, default='predictions', help='Output directory')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 解析目标时间
    target_times = [int(t) for t in args.target_times.split(',')]
    all_times = [args.start_time] + target_times
    
    # 创建预测器
    predictor = IceShapePredictor(args.model_path, args.config_path, args.scaler_path)
    
    # 加载工况数据
    cases_df = pd.read_csv(args.cases_file, sep='\t')
    conditions = cases_df.iloc[args.case_idx].values.astype(np.float32)
    
    print(f"测试工况 {args.case_idx}: {conditions}")
    
    # 加载初始冰形
    case_folder = f"NACA0012_{args.case_idx + 1}"
    initial_shape_file = os.path.join(args.data_root, case_folder, 'result', str(args.start_time), 'beta_0.dat')
    
    if not os.path.exists(initial_shape_file):
        print(f"初始冰形文件不存在: {initial_shape_file}")
        return
    
    # 加载并重采样初始冰形
    dataset = IceShapeDataset(args.data_root, args.cases_file, num_points=200)
    initial_shape = dataset._load_ice_shape(initial_shape_file)
    
    if initial_shape is None:
        print("无法加载初始冰形")
        return
    
    print(f"初始冰形形状: {initial_shape.shape}")
    
    # 预测时间序列
    print("开始预测...")
    predicted_shapes = predictor.predict_sequence(initial_shape, conditions, all_times)
    
    # 加载真实冰形进行对比
    true_shapes = [initial_shape]
    for t in target_times:
        true_shape_file = os.path.join(args.data_root, case_folder, 'result', str(t), 'beta_0.dat')
        if os.path.exists(true_shape_file):
            true_shape = dataset._load_ice_shape(true_shape_file)
            if true_shape is not None:
                true_shapes.append(true_shape)
    
    # 可视化结果
    print("生成可视化...")
    
    # 预测结果
    pred_labels = [f'Pred t={t}' for t in all_times]
    visualize_ice_shapes(
        predicted_shapes, 
        labels=pred_labels,
        save_path=os.path.join(args.output_dir, f'prediction_case_{args.case_idx}.png')
    )
    
    # 对比结果（如果有真实数据）
    if len(true_shapes) > 1:
        comparison_shapes = []
        comparison_labels = []
        
        for i, (pred, true, t) in enumerate(zip(predicted_shapes, true_shapes, all_times)):
            comparison_shapes.extend([pred, true])
            comparison_labels.extend([f'Pred t={t}', f'True t={t}'])
        
        visualize_ice_shapes(
            comparison_shapes,
            labels=comparison_labels,
            save_path=os.path.join(args.output_dir, f'comparison_case_{args.case_idx}.png')
        )
    
    # 保存预测结果
    for i, (shape, t) in enumerate(zip(predicted_shapes, all_times)):
        output_file = os.path.join(args.output_dir, f'predicted_shape_case_{args.case_idx}_time_{t}.txt')
        np.savetxt(output_file, shape, fmt='%.6f', header='x y z')
        print(f"预测结果保存到: {output_file}")
    
    print("推理完成！")


if __name__ == "__main__":
    main()
