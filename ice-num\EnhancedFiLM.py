import torch
import torch.nn as nn

class EnhancedFiLM(nn.Module):
    """
    改进版 FiLM：
    - 让环境参数 (LWC, MVD, Temperature, AOA) 先经过 MLP 处理，增强非线性表达能力。
    - 然后再生成 `gamma` 和 `beta`，用于调制 Transformer 特征。
    """
    def __init__(self, dim):
        super().__init__()
        # ✅ MLP 处理环境参数（4 -> 16 -> 64 * 2）
        self.mlp = nn.Sequential(
            nn.Linear(4, 16),
            nn.ReLU(),
            nn.Linear(16, dim * 2)  # 输出 2 * dim，拆成 gamma 和 beta
        )

    def forward(self, x, env_params):
        """
        :param x: Transformer 输出特征 (batch_size, num_points, dim)
        :param env_params: 归一化环境参数 (batch_size, 4)
        :return: 经过环境参数调制的特征 (batch_size, num_points, dim)
        """
        gamma_beta = self.mlp(env_params)  # (batch_size, 2 * dim)
        gamma, beta = gamma_beta.chunk(2, dim=-1)  # 拆成 gamma 和 beta

        # ✅ 扩展维度，使其可以广播到 (batch_size, num_points, dim)
        gamma = gamma.unsqueeze(1)  # (batch_size, 1, dim)
        beta = beta.unsqueeze(1)  # (batch_size, 1, dim)

        return gamma * x + beta  # 应用 FiLM 变换

# ✅ 运行测试
if __name__ == "__main__":
    batch_size = 64
    num_points = 519
    feature_dim = 64  # PointTransformer 输出维度
    env_dim = 4  # (LWC, MVD, Temperature, AOA)

    # 生成测试数据
    test_features = torch.randn(batch_size, num_points, feature_dim)
    test_env_params = torch.randn(batch_size, env_dim)

    # 初始化改进版 FiLM
    film_layer = EnhancedFiLM(feature_dim)

    # 运行 FiLM
    output = film_layer(test_features, test_env_params)

    # 打印输出形状
    print("输入特征形状:", test_features.shape)  # (64, 519, 64)
    print("环境参数形状:", test_env_params.shape)  # (64, 4)
    print("输出特征形状:", output.shape)  # (64, 519, 64)
