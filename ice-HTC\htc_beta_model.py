#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTC和Beta预测模型
基于原有的PhysEvolveNet架构，适配新的预测任务
输入：xyz坐标 + 工况参数 + 时间
输出：对流换热系数(HTC) + 水滴收集系数(beta)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class MultiHeadAttention(nn.Module):
    """多头注意力机制"""
    
    def __init__(self, embed_dim, num_heads=4, dropout=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        
        assert self.head_dim * num_heads == embed_dim, "embed_dim must be divisible by num_heads"
        
        self.q_linear = nn.Linear(embed_dim, embed_dim)
        self.k_linear = nn.Linear(embed_dim, embed_dim)
        self.v_linear = nn.Linear(embed_dim, embed_dim)
        self.out_linear = nn.Linear(embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        batch_size, seq_len, embed_dim = x.size()
        
        # 计算Q, K, V
        Q = self.q_linear(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.k_linear(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.v_linear(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / (self.head_dim ** 0.5)
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力权重
        attn_output = torch.matmul(attn_weights, V)
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, seq_len, embed_dim)
        
        return self.out_linear(attn_output)

class TransformerBlock(nn.Module):
    """Transformer块"""
    
    def __init__(self, embed_dim, num_heads=4, ff_dim=None, dropout=0.1):
        super().__init__()
        if ff_dim is None:
            ff_dim = embed_dim * 4
            
        self.attention = MultiHeadAttention(embed_dim, num_heads, dropout)
        self.norm1 = nn.LayerNorm(embed_dim)
        self.norm2 = nn.LayerNorm(embed_dim)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(embed_dim, ff_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(ff_dim, embed_dim),
            nn.Dropout(dropout)
        )
        
    def forward(self, x):
        # 自注意力 + 残差连接
        attn_out = self.attention(x)
        x = self.norm1(x + attn_out)
        
        # 前馈网络 + 残差连接
        ff_out = self.feed_forward(x)
        x = self.norm2(x + ff_out)
        
        return x

class GeometryEncoder(nn.Module):
    """几何特征编码器"""
    
    def __init__(self, xyz_dim=3, embed_dim=128, num_layers=2, num_heads=4, dropout=0.1):
        super().__init__()
        
        # 几何特征投影
        self.xyz_projection = nn.Sequential(
            nn.Linear(xyz_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, embed_dim),
            nn.Dropout(dropout)
        )
        
        # Transformer编码器层
        self.transformer_layers = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, dropout=dropout)
            for _ in range(num_layers)
        ])
        
        self.norm = nn.LayerNorm(embed_dim)
        
    def forward(self, xyz):
        """
        Args:
            xyz: [batch_size, 3] - xyz坐标
        Returns:
            [batch_size, embed_dim] - 几何特征
        """
        # 投影到嵌入空间
        x = self.xyz_projection(xyz)  # [batch_size, embed_dim]
        x = x.unsqueeze(1)  # [batch_size, 1, embed_dim] 为了使用transformer
        
        # 通过Transformer层
        for layer in self.transformer_layers:
            x = layer(x)
        
        x = self.norm(x)
        return x.squeeze(1)  # [batch_size, embed_dim]

class ConditionEncoder(nn.Module):
    """工况条件编码器"""
    
    def __init__(self, condition_dim=5, embed_dim=128, dropout=0.1):
        super().__init__()
        
        self.condition_projection = nn.Sequential(
            nn.Linear(condition_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
    def forward(self, conditions):
        """
        Args:
            conditions: [batch_size, 5] - [velocity, temperature, lwc, mvd, time]
        Returns:
            [batch_size, embed_dim] - 条件特征
        """
        return self.condition_projection(conditions)

class FusionModule(nn.Module):
    """特征融合模块"""
    
    def __init__(self, embed_dim=128, dropout=0.1):
        super().__init__()
        
        # 交叉注意力机制
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=embed_dim, 
            num_heads=4, 
            dropout=dropout,
            batch_first=True
        )
        
        # 融合网络
        self.fusion_net = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim * 2, embed_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.norm = nn.LayerNorm(embed_dim)
        
    def forward(self, geo_features, cond_features):
        """
        Args:
            geo_features: [batch_size, embed_dim] - 几何特征
            cond_features: [batch_size, embed_dim] - 条件特征
        Returns:
            [batch_size, embed_dim] - 融合特征
        """
        # 为交叉注意力添加序列维度
        geo_feat = geo_features.unsqueeze(1)  # [batch_size, 1, embed_dim]
        cond_feat = cond_features.unsqueeze(1)  # [batch_size, 1, embed_dim]
        
        # 交叉注意力：几何特征作为query，条件特征作为key和value
        attn_out, _ = self.cross_attention(geo_feat, cond_feat, cond_feat)
        attn_out = attn_out.squeeze(1)  # [batch_size, embed_dim]
        
        # 特征拼接和融合
        fused = torch.cat([geo_features, attn_out], dim=-1)  # [batch_size, embed_dim*2]
        fused = self.fusion_net(fused)  # [batch_size, embed_dim]
        
        # 残差连接
        output = self.norm(fused + geo_features)
        
        return output

class HTCBetaPredictor(nn.Module):
    """HTC和Beta预测头"""
    
    def __init__(self, embed_dim=128, hidden_dim=256, dropout=0.1):
        super().__init__()
        
        # 共享的特征提取层
        self.shared_layers = nn.Sequential(
            nn.Linear(embed_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # HTC预测头
        self.htc_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # Beta预测头
        self.beta_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
    def forward(self, fused_features):
        """
        Args:
            fused_features: [batch_size, embed_dim] - 融合特征
        Returns:
            htc: [batch_size, 1] - HTC预测
            beta: [batch_size, 1] - Beta预测
        """
        shared_feat = self.shared_layers(fused_features)
        
        htc = self.htc_head(shared_feat)
        beta = self.beta_head(shared_feat)
        
        return htc.squeeze(-1), beta.squeeze(-1)

class HTCBetaNet(nn.Module):
    """完整的HTC和Beta预测网络"""
    
    def __init__(self, xyz_dim=3, condition_dim=5, embed_dim=128, 
                 num_geo_layers=2, num_heads=4, hidden_dim=256, dropout=0.1):
        super().__init__()
        
        # 各个模块
        self.geometry_encoder = GeometryEncoder(
            xyz_dim=xyz_dim, 
            embed_dim=embed_dim, 
            num_layers=num_geo_layers,
            num_heads=num_heads,
            dropout=dropout
        )
        
        self.condition_encoder = ConditionEncoder(
            condition_dim=condition_dim,
            embed_dim=embed_dim,
            dropout=dropout
        )
        
        self.fusion_module = FusionModule(
            embed_dim=embed_dim,
            dropout=dropout
        )
        
        self.predictor = HTCBetaPredictor(
            embed_dim=embed_dim,
            hidden_dim=hidden_dim,
            dropout=dropout
        )
        
    def forward(self, xyz, conditions):
        """
        Args:
            xyz: [batch_size, 3] - xyz坐标
            conditions: [batch_size, 5] - [velocity, temperature, lwc, mvd, time]
        Returns:
            htc: [batch_size] - HTC预测
            beta: [batch_size] - Beta预测
        """
        # 编码几何和条件特征
        geo_features = self.geometry_encoder(xyz)
        cond_features = self.condition_encoder(conditions)
        
        # 特征融合
        fused_features = self.fusion_module(geo_features, cond_features)
        
        # 预测HTC和Beta
        htc, beta = self.predictor(fused_features)
        
        return htc, beta

# 测试代码
if __name__ == "__main__":
    print("测试HTCBetaNet模型...")
    
    # 创建模型
    model = HTCBetaNet(
        xyz_dim=3,
        condition_dim=5,
        embed_dim=128,
        num_geo_layers=2,
        num_heads=4,
        hidden_dim=256,
        dropout=0.1
    )
    
    # 创建测试数据
    batch_size = 32
    xyz = torch.randn(batch_size, 3)  # xyz坐标
    conditions = torch.randn(batch_size, 5)  # 工况条件
    
    # 前向传播
    htc_pred, beta_pred = model(xyz, conditions)
    
    print(f"输入xyz形状: {xyz.shape}")
    print(f"输入conditions形状: {conditions.shape}")
    print(f"输出HTC形状: {htc_pred.shape}")
    print(f"输出Beta形状: {beta_pred.shape}")
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    
    print("模型测试完成！")
